# Captioner Esterni e Valutazione CLIP

Questo documento descrive i captioner esterni utilizzati nel progetto e la metodologia di valutazione CLIP.

## Captioner Esterni

Nel progetto sono stati utilizzati tre captioner esterni per generare didascalie per le immagini SVG:

### 1. BLIP

- **Modello:** Salesforce/blip-image-captioning-large
- **Tipo:** Encoder-decoder
- **Architettura:** Vision Transformer (ViT) + BERT
- **Dimensione:** 400M parametri
- **Training:** Addestrato su un mix di dataset di immagini e testo (COCO, Visual Genome, Conceptual Captions, SBU)
- **Caratteristiche:**
  - Utilizza un approccio di bootstrap per migliorare l'allineamento tra immagine e testo
  - Genera didascalie dettagliate e accurate
  - Richiede il rendering dell'SVG in formato immagine

### 2. ViT-GPT2

- **Modello:** nlpconnect/vit-gpt2-image-captioning
- **Tipo:** Encoder-decoder
- **Architettura:** Vision Transformer (ViT) + GPT-2
- **Dimensione:** ~150M parametri
- **Training:** Addestrato principalmente su COCO
- **Caratteristiche:**
  - Combina un encoder visivo (ViT) con un decoder linguistico (GPT-2)
  - Genera didascalie più brevi e dirette
  - Richiede il rendering dell'SVG in formato immagine

### 3. CogVLM

- **Modello:** THUDM/cogvlm-chat-hf
- **Tipo:** Vision-language model
- **Architettura:** Vision Transformer + LLM
- **Dimensione:** 17B parametri
- **Training:** Addestrato su un ampio dataset multimodale
- **Caratteristiche:**
  - Modello più grande e potente
  - Supporta sia captioning che visual question answering
  - Richiede il rendering dell'SVG in formato immagine
  - Può generare didascalie più dettagliate e contestualizzate

## Processo di Valutazione

### Preprocessing

1. **Rendering SVG:**
   - Gli SVG vengono renderizzati in immagini PNG utilizzando diverse librerie:
     - CairoSVG
     - svglib
     - Inkscape (come fallback)
   - Risoluzione: 512x512 pixel
   - Sfondo: bianco

2. **Preparazione Input:**
   - BLIP: Immagine normalizzata secondo i requisiti del modello
   - ViT-GPT2: Immagine normalizzata secondo i requisiti del modello
   - CogVLM: Immagine + prompt "Describe this image in detail"

### Generazione Didascalie

Per ogni SVG nel dataset di test:

1. L'SVG viene renderizzato in formato immagine
2. L'immagine viene passata a ciascun captioner
3. Ogni captioner genera una didascalia
4. Le didascalie generate vengono salvate insieme alla didascalia ground truth

### Metriche di Valutazione

#### Metriche Tradizionali

- **BLEU-4:** Misura la precisione n-gram tra la didascalia generata e quella di riferimento
- **ROUGE-L:** Misura la recall della sottosequenza comune più lunga
- **METEOR:** Considera sinonimi, stemming e altre corrispondenze semantiche
- **CIDEr:** Misura la similarità basata su TF-IDF, specifica per la valutazione di didascalie

#### CLIP Score

Il CLIP Score è una metrica che misura la similarità semantica tra un'immagine e una didascalia utilizzando il modello CLIP (Contrastive Language-Image Pre-training).

**Processo di calcolo:**

1. **Estrazione Features:**
   - L'immagine SVG renderizzata viene codificata con l'encoder visivo di CLIP
   - La didascalia (generata o ground truth) viene codificata con l'encoder testuale di CLIP

2. **Calcolo Similarità:**
   - Si calcola la similarità coseno tra i due embedding
   - Il risultato è un valore tra 0 e 1, dove 1 indica perfetta corrispondenza

3. **Normalizzazione:**
   - I punteggi vengono normalizzati per facilitare il confronto tra diversi modelli

**Vantaggi del CLIP Score:**

- Valuta la corrispondenza semantica piuttosto che la corrispondenza letterale
- Meno sensibile a riformulazioni che mantengono lo stesso significato
- Correlato meglio con il giudizio umano rispetto alle metriche tradizionali

## Implementazione

### Script Principale

Il processo di valutazione è implementato nello script `run_external_captioners_prerendered.slurm`, che:

1. Renderizza gli SVG in immagini
2. Carica i modelli BLIP, ViT-GPT2 e CogVLM
3. Genera didascalie per ogni immagine
4. Calcola le metriche tradizionali (BLEU-4, ROUGE-L, METEOR, CIDEr)
5. Calcola il CLIP Score per ogni didascalia generata e per la didascalia ground truth
6. Salva i risultati in formato JSON e CSV

### Calcolo CLIP Score

Il CLIP Score viene calcolato utilizzando il modello `openai/clip-vit-base-patch32`:

```python
def calculate_clip_score(image_path, caption, clip_model, clip_processor):
    """Calcola il CLIP score tra un'immagine e una didascalia."""
    try:
        # Carica l'immagine
        image = Image.open(image_path).convert("RGB")
        
        # Prepara gli input per CLIP
        inputs = clip_processor(
            text=[caption],
            images=image,
            return_tensors="pt",
            padding=True
        ).to(device)
        
        # Calcola gli embedding
        with torch.no_grad():
            outputs = clip_model(**inputs)
            
        # Calcola la similarità coseno
        logits_per_image = outputs.logits_per_image
        clip_score = logits_per_image.item()
        
        return clip_score
    except Exception as e:
        print(f"Errore nel calcolo del CLIP score: {e}")
        return 0.0
```

## Analisi dei Risultati

I risultati della valutazione vengono analizzati per:

1. **Confronto tra captioner:**
   - Quale captioner genera le didascalie più accurate secondo le diverse metriche
   - Come si comportano i captioner su SVG di diversa complessità

2. **Confronto con i modelli fine-tuned:**
   - Come si confrontano i captioner esterni con i modelli Llama e Gemma fine-tuned
   - Vantaggi e svantaggi dell'approccio diretto (XML) vs rendering

3. **Analisi del CLIP Score:**
   - Correlazione tra CLIP Score e altre metriche
   - Efficacia del CLIP Score come metrica di valutazione per SVG captioning

## Limitazioni e Sfide

1. **Rendering SVG:**
   - Alcune librerie hanno difficoltà con SVG complessi
   - Possibile perdita di dettagli durante il rendering

2. **Captioner non specializzati:**
   - I captioner esterni sono addestrati su immagini fotografiche, non su grafica vettoriale
   - Possono mancare dettagli specifici degli SVG (path, forme geometriche, etc.)

3. **CLIP Score:**
   - CLIP è addestrato principalmente su immagini fotografiche
   - Potrebbe non catturare completamente le caratteristiche specifiche degli SVG

## Conclusioni e Raccomandazioni

1. **Utilizzo di captioner specializzati:**
   - Addestrare captioner specifici per grafica vettoriale potrebbe migliorare i risultati

2. **Miglioramento del rendering:**
   - Testare diverse tecniche di rendering per preservare i dettagli degli SVG

3. **Metriche personalizzate:**
   - Sviluppare metriche specifiche per la valutazione di didascalie di SVG
   - Combinare CLIP Score con metriche che considerano la struttura vettoriale
