# Guida per il modello successivo: Progetto SVG Captioning

## Panoramica del progetto
Questo progetto si concentra sul fine-tuning di Large Language Models (LLMs) per la generazione di didascalie (caption) per immagini SVG. Il progetto utilizza principalmente Llama 3.1 8B e Gemma 2 9B IT, con tecniche di fine-tuning basate su LoRA e tokenizer personalizzati.

## Infrastruttura e ambiente

### Cluster di calcolo
- Sistema: Cluster HPC con SLURM
- Partizioni principali: `boost_usr_prod` (GPU ad alta memoria) e `all_usr_prod` (GPU standard)
- Ambiente Python: `/work/tesi_ediluzio/svg_captioning_env/bin/python`
- Token Hugging Face: `*************************************`
- API key Weights & Biands: `6006c12f16afe29f1402ea7340dadad0cf62b347`

### Directory principali
- Root: `/work/tesi_ediluzio/`
- Esperimenti: `/work/tesi_ediluzio/experiments/xml_direct_input/`
- Dataset: `/work/tesi_ediluzio/data/processed/xml_format/`
- Output: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/`
- Configurazioni: `/work/tesi_ediluzio/experiments/xml_direct_input/configs/`
- Log: `/work/tesi_ediluzio/logs/`

## Modelli utilizzati
1. **Llama 3.1 8B**: `meta-llama/Meta-Llama-3.1-8B-Instruct`
2. **Gemma 2 9B IT**: `google/gemma-2-9b-it`

## Dataset
- Dataset di SVG con didascalie in formato XML
- Diviso in 5 fasce di complessità basate su:
  - Numero di path
  - Numero di comandi SVG
  - Lunghezza del codice
- Split: 70% training, 15% validation, 15% test

## Configurazione del training

### Parametri di training
- Batch size per GPU: 8
- Gradient accumulation steps: 2
- Numero di GPU: 2
- Batch size effettivo: 32 (8 × 2 × 2)
- Learning rate: 1e-5 (Llama), 8e-6 (Gemma)
- Epoche: 3-10 con early stopping
- Valutazione: ogni 50-500 step
- Salvataggio checkpoint: ogni 100-500 step

### Configurazione LoRA
- `lora_r`: 16
- `lora_alpha`: 32
- `lora_dropout`: 0.05
- Target modules: tutti i moduli lineari principali
  ```
  ["down_proj", "gate_proj", "k_proj", "o_proj", "q_proj", "up_proj", "v_proj"]
  ```

### Convergenza
- Definizione: 20-30 step consecutivi senza miglioramento significativo della loss
- Early stopping patience: 30
- Early stopping threshold: 0.0001

## Workflow del progetto

### 1. Training iniziale
- Training su singola GPU fino a convergenza
- Monitoraggio con Weights & Biands
- Salvataggio dei checkpoint migliori

### 2. Training multi-GPU
- Continuazione del training dai migliori checkpoint
- Utilizzo di 2 GPU in parallelo
- Distributed Data Parallel (DDP) per la sincronizzazione

### 3. Training con tokenizer personalizzato
- Creazione di tokenizer specializzati per SVG
- Training a partire dai modelli convergenti
- Monitoraggio separato su Weights & Biands

### 4. Inferenza e valutazione
- Inferenza zero-shot e con modelli fine-tuned
- Metriche: BLEU, CIDEr, METEOR, CLIP Score
- Analisi per fasce di complessità

## Script principali

### Training
- `train_llama_from_scratch.slurm`: Training Llama da zero
- `train_gemma_from_scratch.slurm`: Training Gemma da zero
- `train_lora_multi_gpu_simple.py`: Training LoRA multi-GPU
- `monitor_convergence_and_launch_custom_token.py`: Monitoraggio convergenza

### Inferenza
- `run_zero_shot_inference.py`: Inferenza zero-shot
- `run_inference_lora.py`: Inferenza con modelli fine-tuned

### Valutazione
- `evaluate_models_with_clip.py`: Valutazione con CLIP Score
- `evaluate_with_clip_score.py`: Script personalizzato per CLIP Score

## Preferenze dell'utente

### Gestione dei file
- Mantenere solo file strettamente necessari
- Rimuovere pesi dei modelli non necessari
- Eliminare automaticamente checkpoint non necessari

### Workflow di training
- Preferenza per job che durano 24 ore
- Monitoraggio automatico della convergenza
- Utilizzo di tutti e cinque i modelli nei workflow

### Visualizzazione e reporting
- Grafici separati per training con tokenizer personalizzati
- Continuità visiva nei grafici (interpolazione dei dati)
- Report HTML con esempi di SVG e caption generate

## Problemi comuni e soluzioni

### Problemi di memoria
- Utilizzare quantizzazione a 4-bit o 8-bit
- Abilitare gradient checkpointing
- Ridurre batch size o aumentare gradient accumulation

### Problemi di convergenza
- Aumentare patience dell'early stopping
- Ridurre learning rate
- Monitorare il rapporto tra training loss e validation loss

### Problemi con SLURM
- Utilizzare partizioni alternative se una è sovraccarica
- Ridurre tempo richiesto per aumentare priorità
- Specificare vincoli hardware solo se necessario

## Metriche di valutazione
- BLEU, CIDEr, METEOR: Metriche standard per caption generation
- CLIP Score: Similarità semantica tra SVG e caption
- Rapporto di overfitting: Rapporto tra validation loss e training loss

## Note finali
- Il progetto è in continua evoluzione
- I modelli con tokenizer personalizzati sono ancora in fase di sviluppo
- La valutazione con CLIP Score è stata recentemente implementata
- L'utente preferisce un approccio autonomo e proattivo
