# Valutazione dei Captioner Esterni

## Panoramica
Questo modulo implementa una valutazione completa di modelli di Image Captioning esterni su un dataset di immagini SVG. Il sistema valuta le performance di diversi modelli pre-addestrati utilizzando metriche standard del settore.

## Modelli Valutati
- **GIT Large COCO**: Modello Microsoft basato su transformer
- **BLIP Large**: Modello Salesforce per image captioning
- **ViT-GPT2**: Modello basato su Vision Transformer e GPT-2

## Metriche di Valutazione
1. **BLEU (1-4)**: Valuta la precisione n-gram tra caption generate e reference
2. **METEOR**: Considera sinonimi e flessione delle parole
3. **CIDEr**: Metriche specifiche per image captioning
4. **ROUGE-L**: Valuta la sovrapposizione di sequenze più lunghe
5. **CLIP Score**: Valuta la similarità semantica tra immagine e caption

## Pipeline di Valutazione
1. **Preprocessing**:
   - Rasterizzazione SVG in immagini PNG
   - Configurazione DPI personalizzabile
   - Gestione batch per ottimizzazione

2. **Generazione Caption**:
   - Caricamento modelli su GPU
   - Generazione caption per ogni immagine
   - Salvataggio incrementale dei risultati

3. **Calcolo Metriche**:
   - Calcolo parallelo delle metriche
   - Gestione errori e fallback
   - Normalizzazione dei punteggi

4. **Visualizzazione**:
   - Generazione grafico radar
   - Salvataggio risultati in JSON
   - Logging dettagliato

## Configurazione Tecnica
```python
# Risorse Hardware
- GPU: NVIDIA L40S (24GB+ VRAM)
- CPU: 8 cores
- RAM: 48GB

# Parametri SLURM
- QOS: all_qos_dbg
- Time Limit: 10 ore
- Batch Size: 32
- Workers: 4
```

## Gestione Errori
- Rasterizzazione SVG fallita
- Caricamento modello fallito
- Generazione caption fallita
- Calcolo metriche fallito

## Output
1. **File JSON**:
   - `generated_captions_{model}.json`: Caption generate
   - `evaluation_metrics_summary.json`: Metriche complete

2. **Visualizzazione**:
   - `external_captioners_radar_chart.png`: Grafico radar comparativo

## Note Tecniche
- Utilizzo di `cairosvg` per rasterizzazione
- Pipeline HuggingFace per inferenza
- Metriche da libreria `evaluate`
- Gestione memoria ottimizzata per GPU 