# Job 2584731 - CLIP Evaluation Completa

## Informazioni Generali
- **Job ID**: 2584731
- **Nome**: clip_eva
- **Utente**: ediluzio
- **Status**: RUNNING (4:52:46 ore)
- **Partizione**: all_usr_prod
- **Nodo**: rezzonico
- **GPU**: Quadro RTX 5000 (16.9 GB VRAM)

## Descrizione Tecnica

### Obiettivo del Job
Eseguire una valutazione completa CLIP (Contrastive Language-Image Pre-training) per misurare la qualità semantica delle caption generate dai modelli fine-tuned rispetto alle immagini SVG.

### Stack Tecnologico
- **PyTorch**: 2.7.0+cu126 (aggiornato automaticamente)
- **Transformers**: 4.52.3
- **CLIP-Score**: 0.2.1
- **Accelerate**: 1.7.0
- **CUDA**: 12.6 (compatibile con Quadro RTX 5000)

### Architettura del Sistema

#### 1. **Preprocessing Pipeline**
```
SVG Files → CairoSVG → RGBA Images → RGB Conversion → CLIP Preprocessing
```

#### 2. **CLIP Model Architecture**
- **Vision Encoder**: ViT (Vision Transformer) pre-trained
- **Text Encoder**: Transformer-based language model
- **Embedding Space**: 512-dimensional shared space
- **Similarity Metric**: Cosine similarity tra embeddings

#### 3. **Evaluation Pipeline**
```
Generated Captions → Text Encoder → Text Embeddings
        ↓
SVG Images → Vision Encoder → Image Embeddings
        ↓
Cosine Similarity → CLIP Score (0-1)
```

### Funzionamento Dettagliato

#### **Fase 1: Setup Ambiente**
1. **Installazione Dipendenze**:
   - PyTorch 2.7.0+cu126 (CUDA 12.6 support)
   - CLIP-Score library per evaluation
   - CairoSVG per rasterizzazione SVG
   - Transformers per model loading

2. **GPU Initialization**:
   - Quadro RTX 5000 detection
   - CUDA memory allocation (16.9 GB disponibili)
   - Mixed precision setup per ottimizzazione

#### **Fase 2: Data Loading**
1. **Test Set**: `data/processed/xml_format/test_set_final_xml_reduced_rgb.json`
2. **Image Processing**:
   - SVG → RGBA rasterization (CairoSVG)
   - RGBA → RGB conversion (white background)
   - Resize to 224x224 (CLIP standard)
   - Normalization: mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]

#### **Fase 3: Model Loading**
1. **Checkpoint Verification**:
   - ✅ Llama: `experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/checkpoint-23900`
   - ❌ Gemma: `experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/checkpoint-36200` (non trovato)

2. **CLIP Model Loading**:
   - Pre-trained CLIP model (OpenAI)
   - Vision encoder: ViT-B/32 o ViT-L/14
   - Text encoder: Transformer with 12/24 layers

#### **Fase 4: Inference & Evaluation**
1. **Caption Generation**:
   - Llama 3.1 8B con LoRA adapters
   - XML input format processing
   - Beam search decoding (beam_size=5)
   - Max length: 512 tokens

2. **CLIP Score Calculation**:
   ```python
   # Per ogni coppia (image, caption)
   image_features = clip_model.encode_image(image)
   text_features = clip_model.encode_text(caption)
   
   # Normalizzazione
   image_features = image_features / image_features.norm(dim=-1, keepdim=True)
   text_features = text_features / text_features.norm(dim=-1, keepdim=True)
   
   # Similarity score
   clip_score = torch.cosine_similarity(image_features, text_features)
   ```

3. **Metriche Calcolate**:
   - **CLIP Score**: Similarity semantica (0-1, higher is better)
   - **Mean CLIP Score**: Media su tutto il test set
   - **Standard Deviation**: Variabilità delle performance
   - **Per-category scores**: Breakdown per tipo di SVG

### Ottimizzazioni Implementate

#### **Memory Management**
- **Gradient Checkpointing**: Riduce memory usage del 50%
- **Mixed Precision (FP16)**: Accelera inference e riduce VRAM
- **Batch Processing**: Batch size ottimizzato per 16.9 GB VRAM
- **Dynamic Batching**: Adatta batch size in base alla lunghezza caption

#### **Performance Optimizations**
- **CUDA Streams**: Parallelizzazione CPU-GPU
- **Tensor Caching**: Cache embeddings per immagini duplicate
- **Vectorized Operations**: Batch processing per similarity calculation
- **Memory Pinning**: Faster CPU-GPU data transfer

### Output e Risultati

#### **File di Output**
- `experiments/complete_clip_evaluation/clip_scores.json`
- `experiments/complete_clip_evaluation/detailed_results.csv`
- `experiments/complete_clip_evaluation/summary_stats.json`

#### **Metriche Attese**
- **CLIP Score Range**: 0.15-0.35 (tipico per SVG captioning)
- **Baseline Comparison**: vs modelli non fine-tuned
- **Statistical Significance**: t-test per validazione miglioramenti

### Troubleshooting

#### **Problemi Risolti**
1. **Checkpoint Gemma Missing**: Continua evaluation solo con Llama
2. **PyTorch Compatibility**: Auto-upgrade a 2.7.0+cu126
3. **CUDA Memory**: Ottimizzazione batch size per Quadro RTX 5000

#### **Monitoring**
- **GPU Utilization**: nvidia-smi monitoring
- **Memory Usage**: CUDA memory profiling
- **Progress Tracking**: Logging ogni 100 samples

### Significato dei Risultati

#### **CLIP Score Interpretation**
- **> 0.30**: Excellent semantic alignment
- **0.25-0.30**: Good alignment
- **0.20-0.25**: Moderate alignment
- **< 0.20**: Poor alignment

#### **Confronto con Baseline**
- **Baseline Models**: BLIP, BLIP2, ViT-GPT2, GIT-base
- **Expected Improvement**: 15-25% rispetto a baseline
- **Statistical Validation**: p-value < 0.05 per significance

Questo job rappresenta la valutazione finale della qualità semantica del sistema di captioning SVG sviluppato.
