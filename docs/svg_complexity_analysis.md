# Analisi della Complessità degli SVG

Questo documento presenta un'analisi dettagliata della complessità degli SVG nel dataset e della capacità dei modelli di gestire SVG di diverse lunghezze.

## Sommario

- [Introduzione](#introduzione)
- [Metodologia](#metodologia)
- [Risultati dell'Analisi](#risultati-dellanalisi)
- [Confronto tra Tokenizer](#confronto-tra-tokenizer)
- [Implicazioni per il Training](#implicazioni-per-il-training)
- [Raccomandazioni](#raccomandazioni)
- [Conclusioni](#conclusioni)

## Introduzione

La capacità dei modelli di linguaggio di gestire SVG complessi è cruciale per generare didascalie accurate. Gli SVG possono variare notevolmente in lunghezza e complessità, e quando vengono tokenizzati, possono superare la lunghezza massima del contesto dei modelli. Questa analisi mira a valutare la distribuzione delle lunghezze degli SVG nel dataset e a determinare se i modelli attuali sono in grado di gestire gli SVG più complessi.

## Metodologia

Per analizzare la complessità degli SVG, abbiamo:

1. **Misurato le lunghezze in caratteri** di tutti gli SVG nel dataset di training
2. **Tokenizzato gli SVG** utilizzando i tokenizer di Llama 3.1 8B e Gemma 2 9B IT
3. **Calcolato le statistiche** delle lunghezze in token per entrambi i modelli
4. **Confrontato le lunghezze** con i limiti massimi del contesto dei modelli
5. **Generato grafici di distribuzione** per visualizzare la distribuzione delle lunghezze

Lo script utilizzato per questa analisi è disponibile in `/work/tesi_ediluzio/scripts/analysis/analyze_svg_lengths.py`.

## Risultati dell'Analisi

### Statistiche delle lunghezze in caratteri

- **Minimo**: 198 caratteri
- **Massimo**: 730 caratteri
- **Media**: 474.05 caratteri
- **Mediana**: 483.0 caratteri
- **90° percentile**: 630.0 caratteri
- **95° percentile**: 650.0 caratteri
- **99° percentile**: 694.0 caratteri

### Statistiche delle lunghezze in token per Llama 3.1 8B

- **Minimo**: 80 token
- **Massimo**: 410 token
- **Media**: 213.87 token
- **Mediana**: 217.0 token
- **90° percentile**: 291.0 token
- **95° percentile**: 302.0 token
- **99° percentile**: 326.0 token
- **SVG che superano la lunghezza massima (1024)**: 0 (0.00%)

### Statistiche delle lunghezze in token per Gemma 2 9B IT

- **Minimo**: 98 token
- **Massimo**: 532 token
- **Media**: 324.56 token
- **Mediana**: 328.0 token
- **90° percentile**: 462.0 token
- **95° percentile**: 486.0 token
- **99° percentile**: 513.0 token
- **SVG che superano la lunghezza massima (768)**: 0 (0.00%)

## Confronto tra Tokenizer

L'analisi ha rivelato differenze significative tra i tokenizer di Llama e Gemma nella rappresentazione degli SVG:

1. **Efficienza di tokenizzazione**: Il tokenizer di Llama è più efficiente nella rappresentazione degli SVG, richiedendo in media circa il 66% dei token necessari a Gemma per rappresentare gli stessi SVG.

2. **Distribuzione delle lunghezze**: Entrambi i tokenizer mostrano una distribuzione simile, con una leggera asimmetria verso le lunghezze più brevi.

3. **Rappresentazione di caratteri speciali**: I tokenizer differiscono nella rappresentazione di caratteri speciali comuni negli SVG, come "<", ">", "/", e attributi come "d", "fill", ecc.

4. **Impatto del tokenizer personalizzato**: L'aggiunta di token speciali per SVG al vocabolario del modello potrebbe ridurre ulteriormente il numero di token necessari per rappresentare gli SVG, migliorando l'efficienza.

## Implicazioni per il Training

I risultati dell'analisi hanno importanti implicazioni per il training dei modelli:

1. **Nessun SVG supera la lunghezza massima del contesto**: Sia per Llama (1024 token) che per Gemma (768 token), nessun SVG nel dataset supera la lunghezza massima del contesto. Questo significa che entrambi i modelli possono vedere l'intero SVG durante il training e l'inferenza, il che dovrebbe portare a didascalie più accurate.

2. **Margine di sicurezza**: Anche l'SVG più lungo (532 token per Gemma) è ben al di sotto della lunghezza massima del contesto (768 token), il che fornisce un buon margine di sicurezza.

3. **Efficienza di training**: Il tokenizer di Llama è più efficiente, il che potrebbe portare a un training più veloce e a un minor consumo di memoria.

4. **Impatto sulla qualità delle didascalie**: La capacità di vedere l'intero SVG dovrebbe portare a didascalie più accurate e complete.

## Raccomandazioni

Sulla base dell'analisi, raccomandiamo:

1. **Mantenere le configurazioni attuali**: Poiché nessun SVG supera la lunghezza massima del contesto, non è necessario modificare le configurazioni attuali dei modelli.

2. **Considerare l'aumento della lunghezza massima per dataset futuri**: Se in futuro si prevede di utilizzare SVG più complessi, potrebbe essere utile aumentare la lunghezza massima del contesto, soprattutto per Gemma, che richiede più token.

3. **Monitorare le prestazioni sui diversi percentili**: Potrebbe essere interessante analizzare le prestazioni dei modelli su SVG di diverse lunghezze, per verificare se ci sono differenze significative.

4. **Ottimizzare il tokenizer personalizzato**: L'aggiunta di token speciali per SVG al vocabolario del modello potrebbe ridurre ulteriormente il numero di token necessari per rappresentare gli SVG, migliorando l'efficienza.

## Conclusioni

L'analisi ha dimostrato che entrambi i modelli sono in grado di gestire tutti gli SVG nel dataset senza problemi di lunghezza del contesto. Questo è un ottimo risultato, poiché significa che i modelli possono vedere l'intero SVG durante il training e l'inferenza, il che dovrebbe portare a didascalie più accurate.

Il tokenizer di Llama è più efficiente nella rappresentazione degli SVG, richiedendo in media circa il 66% dei token necessari a Gemma per rappresentare gli stessi SVG. Questo potrebbe portare a un training più veloce e a un minor consumo di memoria.

In sintesi, non è necessario modificare le configurazioni attuali dei modelli per gestire gli SVG nel dataset. Tuttavia, se in futuro si prevede di utilizzare SVG più complessi, potrebbe essere utile aumentare la lunghezza massima del contesto, soprattutto per Gemma, che richiede più token.

---

*Documento creato il 07/05/2025*
