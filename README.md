# Progetto SVG Captioning

Questo progetto si concentra sul fine-tuning di Large Language Models (LLMs) per la generazione di didascalie (caption) per immagini SVG. Il progetto utilizza principalmente Llama 3.1 8B e Gemma 2 9B IT, con tecniche di fine-tuning basate su LoRA e tokenizer personalizzati.

Data di aggiornamento: 12/05/2025

## Struttura del Progetto

Il progetto è organizzato nelle seguenti directory principali:

### Directory Principali

- **`/scripts`**: Script per training, inferenza, valutazione e visualizzazione
  - **`/scripts/cleanup`**: Script per la pulizia dei file e dei checkpoint
  - **`/scripts/inference`**: Script per l'inferenza con modelli addestrati
  - **`/scripts/training`**: Script per il training dei modelli
  - **`/scripts/evaluation`**: Script per la valutazione dei modelli
  - **`/scripts/visualization`**: Script per la creazione di report e visualizzazioni
  - **`/scripts/utils`**: Utility varie
  - **`/scripts/slurm`**: Script SLURM per l'esecuzione su cluster
  - **`/scripts/data_processing`**: Script per la preparazione dei dati

- **`/data`**: Dati di input e output
  - **`/data/processed`**: Dati processati
    - **`/data/processed/xml_format`**: Dati in formato XML
  - **`/data/results`**: Risultati dell'inferenza

- **`/experiments`**: Esperimenti e configurazioni
  - **`/experiments/xml_direct_input`**: Esperimenti con input XML diretto
    - **`/experiments/xml_direct_input/configs`**: Configurazioni per i modelli
    - **`/experiments/xml_direct_input/outputs`**: Output dei modelli addestrati

- **`/evaluation`**: Risultati della valutazione
  - **`/evaluation/checkpoint_metrics`**: Metriche di valutazione per i checkpoint
  - **`/evaluation/reports`**: Report HTML con i risultati
  - **`/evaluation/zero_shot`**: Risultati della valutazione zero-shot

- **`/reports`**: Report e visualizzazioni
  - **`/reports/html`**: Report HTML
  - **`/reports/charts`**: Grafici e visualizzazioni

- **`/docs`**: Documentazione del progetto

- **`/models`**: Modelli salvati

- **`/logs`**: Log dei job SLURM

- **`/clip_module`**: Modulo CLIP per il calcolo del CLIP Score

- **`/shared`**: Codice condiviso
  - **`/shared/svg_core`**: Funzionalità core per SVG
  - **`/shared/utils`**: Utility generiche

- **`/temp`**: File temporanei
  - **`/temp/archive`**: Archivi e backup

- **`/wandb`**: Cache e configurazioni di Weights & Biands

## Modelli Utilizzati

1. **Llama 3.1 8B**: `meta-llama/Meta-Llama-3.1-8B-Instruct`
2. **Gemma 2 9B IT**: `google/gemma-2-9b-it`

## Dataset

- Dataset di SVG con didascalie in formato XML
- Diviso in 5 fasce di complessità basate su:
  - Numero di path
  - Numero di comandi SVG
  - Lunghezza del codice
- Split: 70% training, 15% validation, 15% test

## Configurazione del Training

### Parametri di Training
- Batch size per GPU: 8
- Gradient accumulation steps: 2
- Numero di GPU: 2
- Batch size effettivo: 32 (8 × 2 × 2)
- Learning rate: 1e-5 (Llama), 8e-6 (Gemma)
- Epoche: 3-10 con early stopping
- Valutazione: ogni 50-500 step
- Salvataggio checkpoint: ogni 100-500 step

### Configurazione LoRA
- `lora_r`: 16
- `lora_alpha`: 32
- `lora_dropout`: 0.05
- Target modules: tutti i moduli lineari principali

### Convergenza
- Definizione: 20-30 step consecutivi senza miglioramento significativo della loss
- Early stopping patience: 30
- Early stopping threshold: 0.0001

## Workflow del Progetto

### Pipeline Completa

**IMPORTANTE: È FONDAMENTALE seguire la pipeline completa per ogni ciclo di training e valutazione.**

La pipeline completa è documentata in dettaglio nel file [PIPELINE_COMPLETA.md](/docs/PIPELINE_COMPLETA.md) e include:

1. **Preparazione e Training**:
   - Configurazione dei job SLURM
   - Aggiornamento del log delle run
   - Monitoraggio su Weights & Biands

2. **Valutazione**:
   - Configurazione dei job di valutazione
   - Calcolo delle metriche (BLEU, METEOR, CIDEr, CLIP Score)

3. **Generazione di Report e Grafici**:
   - Creazione di report HTML con esempi qualitativi
   - Generazione di grafici radar per confrontare le metriche
   - Aggiornamento della documentazione

4. **Pulizia e Manutenzione**:
   - Gestione dei checkpoint
   - Aggiornamento dei log

### Fasi del Progetto

#### 1. Training Iniziale
- Training su singola GPU fino a convergenza
- Monitoraggio con Weights & Biands
- Salvataggio dei checkpoint migliori

#### 2. Training Multi-GPU
- Continuazione del training dai migliori checkpoint
- Utilizzo di 2 GPU in parallelo
- Distributed Data Parallel (DDP) per la sincronizzazione

#### 3. Training con Tokenizer Personalizzato
- Creazione di tokenizer specializzati per SVG
- Training a partire dai modelli convergenti
- Monitoraggio separato su Weights & Biands

#### 4. Inferenza e Valutazione
- Inferenza zero-shot e con modelli fine-tuned
- Metriche: BLEU, CIDEr, METEOR, CLIP Score
- Analisi per fasce di complessità

## Metriche di Valutazione

- **Qualità**: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score
- **Efficienza**: Tempo di inferenza, Perplexity
- **Analisi linguistica**: Lunghezza didascalie, Diversità vocabolario, Type-Token Ratio, Self-BLEU

## Risultati Principali Zero-Shot

- **Llama 3.1 8B**: BLEU-1=0.0150, METEOR=0.0345, CIDEr=0.7434
- **Gemma 2 9B IT**: BLEU-1=0.0114, METEOR=0.0291, CIDEr=0.8066

## Implementazioni Recenti

- **CheckpointCleanupCallback**: Elimina automaticamente i checkpoint vecchi durante l'addestramento
- **Supporto per Training Multi-GPU**: Configurazione DeepSpeed per ottimizzare l'uso della memoria
- **Integrazione CLIP Score**: Calcolo della similarità semantica tra SVG e didascalie
- **Monitoraggio Automatico dei Checkpoint**: Script per monitorare e valutare automaticamente i nuovi checkpoint
- **Generazione Automatica di Report HTML**: Script per generare report HTML con esempi qualitativi e metriche
- **Generazione di Grafici Radar**: Script per generare grafici radar per confrontare le metriche tra i modelli
- **Pipeline Completa Documentata**: Documentazione dettagliata della pipeline completa da seguire per ogni esperimento

## Documentazione

Per ulteriori dettagli, consultare i file di documentazione nella directory `/docs`.
