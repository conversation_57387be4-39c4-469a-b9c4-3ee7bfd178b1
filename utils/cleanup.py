#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per la pulizia dei file temporanei.
"""

import os
import argparse
import shutil
import logging
from typing import List, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_pycache_dirs(root_dir: str) -> List[str]:
    """
    Trova tutte le directory __pycache__ nel progetto.
    
    Args:
        root_dir: Directory radice del progetto
        
    Returns:
        Lista di percorsi delle directory __pycache__
    """
    pycache_dirs = []
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # Salta la directory dell'ambiente virtuale
        if "svg_captioning_env" in dirpath:
            continue
            
        if "__pycache__" in dirnames:
            pycache_dirs.append(os.path.join(dirpath, "__pycache__"))
    
    return pycache_dirs

def find_empty_dirs(root_dir: str) -> List[str]:
    """
    Trova tutte le directory vuote nel progetto.
    
    Args:
        root_dir: Directory radice del progetto
        
    Returns:
        Lista di percorsi delle directory vuote
    """
    empty_dirs = []
    for dirpath, dirnames, filenames in os.walk(root_dir, topdown=False):
        # Salta la directory dell'ambiente virtuale
        if "svg_captioning_env" in dirpath:
            continue
            
        if not dirnames and not filenames:
            empty_dirs.append(dirpath)
    
    return empty_dirs

def find_old_logs(log_dir: str, days: int = 7) -> List[str]:
    """
    Trova i file di log più vecchi di un certo numero di giorni.
    
    Args:
        log_dir: Directory dei log
        days: Numero di giorni
        
    Returns:
        Lista di percorsi dei file di log vecchi
    """
    import time
    
    old_logs = []
    current_time = time.time()
    days_in_seconds = days * 24 * 60 * 60
    
    if not os.path.exists(log_dir):
        return []
    
    for filename in os.listdir(log_dir):
        file_path = os.path.join(log_dir, filename)
        if os.path.isfile(file_path):
            file_age = current_time - os.path.getmtime(file_path)
            if file_age > days_in_seconds:
                old_logs.append(file_path)
    
    return old_logs

def cleanup(root_dir: str, log_dir: str, days: int = 7, dry_run: bool = True) -> Tuple[int, int, int]:
    """
    Pulisce i file temporanei.
    
    Args:
        root_dir: Directory radice del progetto
        log_dir: Directory dei log
        days: Numero di giorni per i log vecchi
        dry_run: Se True, mostra solo i file che verrebbero eliminati
        
    Returns:
        Tuple con il numero di directory __pycache__, directory vuote e file di log eliminati
    """
    # Trova le directory __pycache__
    pycache_dirs = find_pycache_dirs(root_dir)
    logger.info(f"Trovate {len(pycache_dirs)} directory __pycache__.")
    
    # Trova le directory vuote
    empty_dirs = find_empty_dirs(root_dir)
    logger.info(f"Trovate {len(empty_dirs)} directory vuote.")
    
    # Trova i file di log vecchi
    old_logs = find_old_logs(log_dir, days)
    logger.info(f"Trovati {len(old_logs)} file di log più vecchi di {days} giorni.")
    
    # Elimina le directory __pycache__
    pycache_count = 0
    for pycache_dir in pycache_dirs:
        if dry_run:
            logger.info(f"[DRY RUN] Eliminazione directory: {pycache_dir}")
        else:
            try:
                shutil.rmtree(pycache_dir)
                logger.info(f"Eliminata directory: {pycache_dir}")
                pycache_count += 1
            except Exception as e:
                logger.error(f"Errore nell'eliminazione di {pycache_dir}: {e}")
    
    # Elimina le directory vuote
    empty_count = 0
    for empty_dir in empty_dirs:
        if dry_run:
            logger.info(f"[DRY RUN] Eliminazione directory vuota: {empty_dir}")
        else:
            try:
                os.rmdir(empty_dir)
                logger.info(f"Eliminata directory vuota: {empty_dir}")
                empty_count += 1
            except Exception as e:
                logger.error(f"Errore nell'eliminazione di {empty_dir}: {e}")
    
    # Elimina i file di log vecchi
    log_count = 0
    for log_file in old_logs:
        if dry_run:
            logger.info(f"[DRY RUN] Eliminazione file di log: {log_file}")
        else:
            try:
                os.remove(log_file)
                logger.info(f"Eliminato file di log: {log_file}")
                log_count += 1
            except Exception as e:
                logger.error(f"Errore nell'eliminazione di {log_file}: {e}")
    
    return pycache_count, empty_count, log_count

def main():
    parser = argparse.ArgumentParser(description="Pulisce i file temporanei.")
    parser.add_argument("--root-dir", type=str, default="/work/tesi_ediluzio", help="Directory radice del progetto.")
    parser.add_argument("--log-dir", type=str, default="/work/tesi_ediluzio/logs", help="Directory dei log.")
    parser.add_argument("--days", type=int, default=7, help="Numero di giorni per i log vecchi.")
    parser.add_argument("--dry-run", action="store_true", help="Mostra solo i file che verrebbero eliminati.")
    
    args = parser.parse_args()
    
    logger.info(f"Pulizia dei file temporanei in {args.root_dir}...")
    logger.info(f"Modalità: {'DRY RUN' if args.dry_run else 'ELIMINAZIONE'}")
    
    pycache_count, empty_count, log_count = cleanup(args.root_dir, args.log_dir, args.days, args.dry_run)
    
    if args.dry_run:
        logger.info(f"[DRY RUN] Sarebbero state eliminate {pycache_count} directory __pycache__, {empty_count} directory vuote e {log_count} file di log.")
    else:
        logger.info(f"Eliminate {pycache_count} directory __pycache__, {empty_count} directory vuote e {log_count} file di log.")

if __name__ == "__main__":
    main()
