# Utility

Questa directory contiene script di utility per il progetto.

## Script Principali

- **monitor_jobs.py**: <PERSON>ript per monitorare i job SLURM
- **cleanup.py**: Script per la pulizia dei file temporanei

## Utilizzo

### Monitoraggio dei Job SLURM

```bash
# Monitoraggio base (aggiornamento ogni 60 secondi)
./monitor_jobs.py

# Monitoraggio con log (aggiornamento ogni 30 secondi, mostra le ultime 20 linee)
./monitor_jobs.py --interval 30 --show-logs --lines 20
```

### Pulizia dei File Temporanei

```bash
# Modalità dry-run (mostra solo i file che verrebbero eliminati)
./cleanup.py --dry-run

# Eliminazione effettiva
./cleanup.py

# Eliminazione dei log più vecchi di 14 giorni
./cleanup.py --days 14
```
