#!/bin/bash

# Script per monitorare l'utilizzo del disco
# Uso: ./monitor_disk_usage.sh [directory]

# Se non viene specificata una directory, usa la directory corrente
DIR=${1:-"/work/tesi_ediluzio"}

echo "=== Monitoraggio Utilizzo Disco per $DIR ==="
echo ""

# Mostra l'utilizzo totale del disco
echo "Utilizzo totale:"
du -sh "$DIR"
echo ""

# Mostra l'utilizzo delle directory principali
echo "Utilizzo per directory principale:"
du -sh "$DIR"/* | sort -hr
echo ""

# Mostra le 10 directory più grandi (limita la profondità a 2 livelli)
echo "Top 10 directory più grandi (profondità 2):"
find "$DIR" -maxdepth 2 -type d -exec du -sh {} \; 2>/dev/null | sort -hr | head -10
echo ""

# Mostra le 10 file più grandi (limita la profondità a 2 livelli)
echo "Top 10 file più grandi (profondità 2):"
find "$DIR" -maxdepth 2 -type f -exec du -sh {} \; 2>/dev/null | sort -hr | head -10
echo ""

# Mostra lo spazio disponibile
echo "Spazio disponibile:"
df -h "$DIR"
echo ""

echo "=== Fine Monitoraggio ==="
