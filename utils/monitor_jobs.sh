#!/bin/bash

# Script per monitorare i job SLURM
# Uso: ./monitor_jobs.sh [user]

# Se non viene specificato un utente, usa l'utente corrente
USER=${1:-$(whoami)}

echo "=== Monitoraggio Job SLURM per l'utente $USER ==="
echo ""

# Mostra i job in esecuzione
echo "Job in esecuzione:"
squeue -u $USER -t RUNNING -o "%.18i %.9P %.8j %.8u %.2t %.10M %.6D %R"
echo ""

# Mostra i job in coda
echo "Job in coda:"
squeue -u $USER -t PENDING -o "%.18i %.9P %.8j %.8u %.2t %.10M %.6D %R"
echo ""

# Mostra i job completati nelle ultime 24 ore
echo "Job completati nelle ultime 24 ore:"
sacct -u $USER -S $(date -d "24 hours ago" +"%Y-%m-%dT%H:%M:%S") -o JobID,JobName,State,ExitCode,Elapsed,MaxRSS,NodeList
echo ""

# Mostra l'utilizzo delle risorse
echo "Utilizzo delle risorse:"
sshare -u $USER
echo ""

echo "=== Fine Monitoraggio ==="
