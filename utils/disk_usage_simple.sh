#!/bin/bash

# Script semplificato per monitorare l'utilizzo del disco
# Uso: ./disk_usage_simple.sh [directory]

# Se non viene specificata una directory, usa la directory corrente
DIR=${1:-"/work/tesi_ediluzio"}

echo "=== Monitoraggio Utilizzo Disco per $DIR ==="
echo ""

# Mostra l'utilizzo totale del disco
echo "Utilizzo totale:"
du -sh "$DIR"
echo ""

# Mostra l'utilizzo delle directory principali
echo "Utilizzo per directory principale:"
du -sh "$DIR"/* | sort -hr
echo ""

# Mostra lo spazio disponibile
echo "Spazio disponibile:"
df -h "$DIR"
echo ""

echo "=== Fine Monitoraggio ==="
