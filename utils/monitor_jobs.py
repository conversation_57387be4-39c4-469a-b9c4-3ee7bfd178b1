#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per monitorare i job SLURM.
"""

import os
import re
import argparse
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional

def get_job_info() -> List[Dict[str, str]]:
    """
    Ottiene informazioni sui job SLURM dell'utente corrente.
    
    Returns:
        Lista di dizionari con informazioni sui job
    """
    try:
        # Esegui il comando squeue per ottenere informazioni sui job
        result = subprocess.run(
            ["squeue", "-u", os.environ.get("USER", ""), "-o", "%i|%j|%T|%M|%N|%R"],
            capture_output=True,
            text=True,
            check=True
        )
        
        # Analizza l'output
        jobs = []
        lines = result.stdout.strip().split("\n")
        
        # Salta l'intestazione
        for line in lines[1:]:
            if not line.strip():
                continue
                
            parts = line.split("|")
            if len(parts) >= 6:
                job_id, job_name, state, time, node, reason = parts[:6]
                jobs.append({
                    "job_id": job_id.strip(),
                    "job_name": job_name.strip(),
                    "state": state.strip(),
                    "time": time.strip(),
                    "node": node.strip(),
                    "reason": reason.strip()
                })
        
        return jobs
    except subprocess.CalledProcessError as e:
        print(f"Errore nell'esecuzione di squeue: {e}")
        return []

def get_job_log_paths(job_id: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Ottiene i percorsi dei file di log per un job SLURM.
    
    Args:
        job_id: ID del job SLURM
        
    Returns:
        Tuple con i percorsi dei file di output e di errore
    """
    try:
        # Esegui il comando scontrol per ottenere informazioni sul job
        result = subprocess.run(
            ["scontrol", "show", "job", job_id],
            capture_output=True,
            text=True,
            check=True
        )
        
        # Cerca i percorsi dei file di log nell'output
        output_match = re.search(r"StdOut=([^\s]+)", result.stdout)
        error_match = re.search(r"StdErr=([^\s]+)", result.stdout)
        
        output_path = output_match.group(1) if output_match else None
        error_path = error_match.group(1) if error_match else None
        
        return output_path, error_path
    except subprocess.CalledProcessError as e:
        print(f"Errore nell'esecuzione di scontrol: {e}")
        return None, None

def tail_log(log_path: str, lines: int = 10) -> str:
    """
    Ottiene le ultime linee di un file di log.
    
    Args:
        log_path: Percorso del file di log
        lines: Numero di linee da ottenere
        
    Returns:
        Ultime linee del file di log
    """
    try:
        if not os.path.exists(log_path):
            return f"File non trovato: {log_path}"
            
        result = subprocess.run(
            ["tail", "-n", str(lines), log_path],
            capture_output=True,
            text=True,
            check=True
        )
        
        return result.stdout
    except subprocess.CalledProcessError as e:
        return f"Errore nell'esecuzione di tail: {e}"

def monitor_jobs(interval: int = 60, show_logs: bool = False, lines: int = 10):
    """
    Monitora i job SLURM dell'utente corrente.
    
    Args:
        interval: Intervallo di aggiornamento in secondi
        show_logs: Se True, mostra le ultime linee dei file di log
        lines: Numero di linee da mostrare per ogni file di log
    """
    try:
        while True:
            # Pulisci lo schermo
            os.system("clear")
            
            # Ottieni informazioni sui job
            jobs = get_job_info()
            
            # Stampa l'intestazione
            print(f"=== Monitoraggio Job SLURM ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ===")
            print(f"{'Job ID':<10} {'Job Name':<20} {'Stato':<10} {'Tempo':<10} {'Nodo':<15} {'Motivo':<20}")
            print("-" * 85)
            
            # Stampa le informazioni sui job
            for job in jobs:
                print(f"{job['job_id']:<10} {job['job_name'][:20]:<20} {job['state']:<10} {job['time']:<10} {job['node'][:15]:<15} {job['reason'][:20]:<20}")
            
            # Mostra i log se richiesto
            if show_logs and jobs:
                print("\n=== Log dei Job ===")
                for job in jobs:
                    job_id = job["job_id"]
                    output_path, error_path = get_job_log_paths(job_id)
                    
                    print(f"\nJob {job_id} ({job['job_name']}):")
                    
                    if output_path:
                        print(f"\nOutput ({output_path}):")
                        print(tail_log(output_path, lines))
                    
                    if error_path:
                        print(f"\nErrore ({error_path}):")
                        print(tail_log(error_path, lines))
            
            # Attendi prima del prossimo aggiornamento
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\nMonitoraggio interrotto.")

def main():
    parser = argparse.ArgumentParser(description="Monitora i job SLURM.")
    parser.add_argument("--interval", type=int, default=60, help="Intervallo di aggiornamento in secondi.")
    parser.add_argument("--show-logs", action="store_true", help="Mostra le ultime linee dei file di log.")
    parser.add_argument("--lines", type=int, default=10, help="Numero di linee da mostrare per ogni file di log.")
    
    args = parser.parse_args()
    monitor_jobs(args.interval, args.show_logs, args.lines)

if __name__ == "__main__":
    main()
