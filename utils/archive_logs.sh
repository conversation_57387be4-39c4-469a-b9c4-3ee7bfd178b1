#!/bin/bash

# Script per archiviare i log più vecchi
# Uso: ./archive_logs.sh [giorni]

# Se non viene specificato un numero di giorni, usa 7 giorni
DAYS=${1:-7}
LOG_DIR="/work/tesi_ediluzio/logs"
ARCHIVE_DIR="${LOG_DIR}/archive"
DATE=$(date +"%Y%m%d")
ARCHIVE_FILE="logs_archive_${DATE}.tar.gz"

echo "=== Archiviazione Log Più Vecchi di ${DAYS} Giorni ==="
echo ""

# Crea la directory di archivio se non esiste
mkdir -p "${ARCHIVE_DIR}"
echo "Directory di archivio: ${ARCHIVE_DIR}"

# Conta i file di log più vecchi
OLD_LOGS=$(find "${LOG_DIR}" -maxdepth 1 -name "*.out" -o -name "*.err" -mtime +${DAYS} | wc -l)
echo "File di log più vecchi di ${DAYS} giorni: ${OLD_LOGS}"

# Se non ci sono file da archiviare, esci
if [ "${OLD_LOGS}" -eq 0 ]; then
    echo "Nessun file da archiviare."
    exit 0
fi

# Sposta i file di log più vecchi nella directory di archivio
echo "Spostamento dei file di log più vecchi nella directory di archivio..."
find "${LOG_DIR}" -maxdepth 1 -name "*.out" -o -name "*.err" -mtime +${DAYS} | xargs -I{} mv {} "${ARCHIVE_DIR}/"

# Comprimi i file di log archiviati
echo "Compressione dei file di log archiviati..."
cd "${ARCHIVE_DIR}" || exit 1
tar -czf "${ARCHIVE_FILE}" *.out *.err 2>/dev/null

# Verifica che l'archivio sia stato creato correttamente
if [ -f "${ARCHIVE_FILE}" ]; then
    echo "Archivio creato: ${ARCHIVE_DIR}/${ARCHIVE_FILE}"
    echo "Dimensione archivio: $(du -h "${ARCHIVE_FILE}" | cut -f1)"
    
    # Rimuovi i file originali
    echo "Rimozione dei file originali..."
    rm -f *.out *.err 2>/dev/null
    
    echo "Archiviazione completata con successo."
else
    echo "Errore durante la creazione dell'archivio."
    exit 1
fi

echo ""
echo "=== Fine Archiviazione ==="
