#!/bin/bash

# Script per fare il backup dei dati importanti
# Uso: ./backup_data.sh [directory_destinazione]

# Se non viene specificata una directory di destinazione, usa la directory corrente
DEST_DIR=${1:-"/work/tesi_ediluzio/backups"}
SRC_DIR="/work/tesi_ediluzio"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="svg_captioner_backup_${TIMESTAMP}.tar.gz"

# Crea la directory di destinazione se non esiste
mkdir -p "$DEST_DIR"

echo "=== Backup dei Dati Importanti ==="
echo "Directory sorgente: $SRC_DIR"
echo "Directory destinazione: $DEST_DIR"
echo "Nome backup: $BACKUP_NAME"
echo ""

# Crea una lista dei file e directory da includere nel backup
echo "File e directory da includere nel backup:"
echo "- Configurazioni (*.json, *.yaml, *.yml)"
echo "- <PERSON>ript Python (*.py)"
echo "- Script SLURM (*.slurm)"
echo "- File di testo (*.txt, *.md)"
echo "- Risultati (results/)"
echo "- Modelli addestrati (experiments/xml_direct_input/outputs/)"
echo ""

# Crea il backup
echo "Creazione backup in corso..."
tar -czf "$DEST_DIR/$BACKUP_NAME" \
    --exclude="*.tar.gz" \
    --exclude="*.log" \
    --exclude="*.out" \
    --exclude="*.err" \
    --exclude="__pycache__" \
    --exclude="*.pyc" \
    --exclude="svg_captioning_env" \
    --exclude="chunks" \
    -C "$SRC_DIR" \
    data/processed/xml_format \
    experiments/xml_direct_input/configs \
    experiments/xml_direct_input/outputs \
    results \
    shared \
    utils \
    *.py \
    *.slurm \
    *.json \
    *.yaml \
    *.yml \
    *.txt \
    *.md

# Verifica se il backup è stato creato con successo
if [ $? -eq 0 ]; then
    echo "Backup completato con successo: $DEST_DIR/$BACKUP_NAME"
    echo "Dimensione backup: $(du -sh "$DEST_DIR/$BACKUP_NAME" | cut -f1)"
else
    echo "Errore durante la creazione del backup"
fi

echo "=== Fine Backup ==="
