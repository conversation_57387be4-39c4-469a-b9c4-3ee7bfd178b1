import json
import requests
import time
import os
from tqdm import tqdm

# Funzione per eseguire l'inferenza utilizzando l'API di Hugging Face
def run_inference_api(model_id, svg_data, api_key):
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Crea il prompt per l'inferenza
    prompt = f"""Genera una didascalia concisa e descrittiva per il seguente codice SVG:

{svg_data}

Didascalia:"""
    
    # Adatta il payload in base al modello
    if "gemma" in model_id.lower():
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": 150,
                "temperature": 0.6,
                "top_p": 0.9,
                "do_sample": True
            }
        }
    else:  # Llama
        messages = [{"role": "user", "content": prompt}]
        payload = {
            "inputs": messages,
            "parameters": {
                "max_new_tokens": 150,
                "temperature": 0.6,
                "top_p": 0.9,
                "do_sample": True
            }
        }
    
    # Esegui la richiesta API
    start_time = time.time()
    response = requests.post(
        f"https://api-inference.huggingface.co/models/{model_id}",
        headers=headers,
        json=payload
    )
    inference_time = time.time() - start_time
    
    # Gestisci la risposta
    if response.status_code == 200:
        result = response.json()
        
        # Estrai la risposta generata in base al formato della risposta
        if "gemma" in model_id.lower():
            generated_text = result[0]["generated_text"]
            # Rimuovi il prompt dalla risposta
            if generated_text.startswith(prompt):
                generated_text = generated_text[len(prompt):].strip()
        else:  # Llama
            generated_text = result[0]["generated_text"]
        
        return generated_text, inference_time
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return f"Error: {response.status_code}", 0

def main():
    # Carica l'API key
    api_key = "*************************************"
    
    # Modelli da utilizzare
    models = [
        "meta-llama/Llama-3.1-8B-Instruct",
        "google/gemma-2-9b-it"
    ]
    
    # Carica i dati SVG complex
    with open("evaluation/complex_svg_llama.json", "r") as f:
        data = json.load(f)
    
    # Crea le directory di output se non esistono
    os.makedirs("evaluation/zero_shot", exist_ok=True)
    
    # Esegui l'inferenza per ogni modello
    for model_id in models:
        print(f"Running inference for {model_id}...")
        
        # Nome del file di output
        if "llama" in model_id.lower():
            output_file = "evaluation/zero_shot/Llama-3.1-8B-Instruct_complex_zero_shot_examples.json"
        else:
            output_file = "evaluation/zero_shot/gemma-2-9b-it_complex_zero_shot_examples.json"
        
        # Esegui l'inferenza per ogni esempio
        results = []
        for item in tqdm(data):
            svg_data = item["xml"]
            true_caption = item["caption"]
            item_id = item["id"]
            
            # Esegui l'inferenza
            generated_caption, inference_time = run_inference_api(model_id, svg_data, api_key)
            
            # Crea il risultato
            result = {
                "id": item_id,
                "complexity": "complex",
                "svg": svg_data,
                "true_caption": true_caption,
                "generated_caption": generated_caption,
                "inference_time": inference_time,
                "model": model_id.split("/")[-1]
            }
            
            results.append(result)
        
        # Salva i risultati
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to {output_file}")

if __name__ == "__main__":
    main()
