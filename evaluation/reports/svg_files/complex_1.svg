<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
    </linearGradient>
    <filter id="blur1" x="0" y="0">
      <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
    </filter>
  </defs>
  <circle cx="256" cy="256" r="200" fill="url(#grad1)" />
  <rect x="156" y="156" width="200" height="200" fill="rgba(255,255,0,0.5)" filter="url(#blur1)" />
  <path d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" fill="none" stroke="white" stroke-width="5" />
</svg>
