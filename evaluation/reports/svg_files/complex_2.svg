<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="grad2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" style="stop-color:rgb(255,255,255);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgb(0,0,0);stop-opacity:1" />
    </radialGradient>
    <filter id="shadow1" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="10" dy="10" stdDeviation="10" flood-color="#000" flood-opacity="0.5" />
    </filter>
  </defs>
  <rect x="56" y="56" width="400" height="400" rx="20" ry="20" fill="url(#grad2)" filter="url(#shadow1)" />
  <circle cx="156" cy="156" r="50" fill="red" />
  <circle cx="356" cy="156" r="50" fill="green" />
  <circle cx="156" cy="356" r="50" fill="blue" />
  <circle cx="356" cy="356" r="50" fill="yellow" />
  <path d="M256,156 L356,256 L256,356 L156,256 Z" fill="purple" />
</svg>
