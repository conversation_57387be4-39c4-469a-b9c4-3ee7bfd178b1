<!DOCTYPE html>
<html>
<head>
    <title>Report di Inferenza SVG Captioning - Llama 3.1 8B Fine-Tuned</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
            text-align: center;
        }
        h2 {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #ddd;
        }
        h3 {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            color: #555;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin: 0 auto;
            background-color: #f9f9f9;
        }
        .example {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .caption {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .ground-truth {
            color: #2c7fb8;
        }
        .generated {
            color: #31a354;
        }
        .model-info {
            color: #636363;
            font-style: italic;
            margin-top: 5px;
        }
        .metrics {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .complexity-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #fafafa;
            border-radius: 5px;
            border-left: 5px solid #ddd;
        }
        .simple-section {
            border-left-color: #a1d99b;
        }
        .medium-section {
            border-left-color: #fdae6b;
        }
        .complex-section {
            border-left-color: #e6550d;
        }
    </style>
</head>
<body>
    <div class="container">
    <h1>Report di Inferenza SVG Captioning - Llama 3.1 8B Fine-Tuned</h1>
    <p style="text-align: center;">Report generato il 05/05/2025 12:00:00</p>
    <div style="max-width: 800px; margin: 20px auto; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #4285f4;">
        <h3 style="margin-top: 0; color: #4285f4;">Informazioni sul Report</h3>
        <p>Questo report contiene esempi di inferenza per il modello Llama 3.1 8B fine-tuned su immagini SVG di diverse complessità.</p>
        <p>Per ogni esempio sono riportate:</p>
        <ul>
            <li>L'immagine SVG</li>
            <li>La caption ground truth</li>
            <li>La caption generata dal modello</li>
        </ul>
        <p>Gli esempi sono organizzati per complessità (simple, medium, complex).</p>
        <p>Il modello ha ottenuto i seguenti risultati di valutazione:</p>
        <ul>
            <li><strong>BLEU-1:</strong> 0.4644</li>
            <li><strong>BLEU-2:</strong> 0.3307</li>
            <li><strong>BLEU-3:</strong> 0.2455</li>
            <li><strong>BLEU-4:</strong> 0.1652</li>
            <li><strong>METEOR:</strong> 0.4745</li>
            <li><strong>CIDEr:</strong> 0.8078</li>
        </ul>
        <div style="text-align: center; margin-top: 20px;">
            <img src="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/metrics_radar.png" alt="Radar Chart delle Metriche" style="max-width: 100%; height: auto;">
        </div>
    </div>

    <div class="complexity-section simple-section">
        <h2>Complessità: Simple</h2>

        <div class="model-section">
            <h3>Llama 3.1 8B (Fine-Tuned)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/simple_example_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a circle
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a simple geometric shape, specifically a circle, which is colored a solid dark blue
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/simple_example_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a vertical bar chart with a single green bar
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a green vertical bar that extends from the top to the bottom of the image
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/simple_example_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a black triangle pointing upwards
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a simple geometric shape, specifically a right triangle with a black border
                </div>
            </div>
        </div>
    </div>

    <div class="complexity-section medium-section">
        <h2>Complessità: Medium</h2>

        <div class="model-section">
            <h3>Llama 3.1 8B (Fine-Tuned)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/medium_example_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, minimalist icon that appears to be a graphical representation of an upward arrow
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a simple, minimalistic icon that represents a file folder
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/medium_example_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, minimalist design of a lightning bolt
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a simple, minimalist arrow pointing to the left
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/medium_example_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a flag with a simple yet distinctive design
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a flag with a horizontal layout
                </div>
            </div>
        </div>
    </div>

    <div class="complexity-section complex-section">
        <h2>Complessità: Complex</h2>

        <div class="model-section">
            <h3>Llama 3.1 8B (Fine-Tuned)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/complex_example_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, iconic symbol of a circular arrow forming a loop
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a simple, bold, and geometric logo consisting of two intersecting arrows
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/complex_example_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, yet bold and striking graphic design featuring a large, uppercase letter "M" in a vibrant purple background
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a simple geometric shape, specifically a circle, which is divided into two distinct sections
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-container">
                    <img src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/complex_example_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a stylized, abstract design featuring a combination of curved lines and shapes
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> The image depicts a simple geometric shape consisting of two main components: a vertical blue rectangle and a horizontal green rectangle
                </div>
            </div>
        </div>
    </div>

    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente.</p>
        <p>© 2025 Università di Modena e Reggio Emilia</p>
    </footer>
    </div>
</body>
</html>
