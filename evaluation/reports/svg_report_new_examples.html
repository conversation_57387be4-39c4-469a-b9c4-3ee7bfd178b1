<!DOCTYPE html>
<html>
<head>
    <title>Report di Inferenza SVG Captioning - Nuovi Esempi</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
            text-align: center;
        }
        h2 {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #ddd;
        }
        h3 {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            color: #555;
        }
        .svg-placeholder {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin: 0 auto;
            background-color: #f9f9f9;
        }
        .example {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .caption {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .ground-truth {
            color: #2c7fb8;
        }
        .generated {
            color: #31a354;
        }
        .model-info {
            color: #636363;
            font-style: italic;
            margin-top: 5px;
        }
        .metrics {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .complexity-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #fafafa;
            border-radius: 5px;
            border-left: 5px solid #ddd;
        }
        .simple-section {
            border-left-color: #a1d99b;
        }
        .medium-section {
            border-left-color: #fdae6b;
        }
    </style>
</head>
<body>
    <div class="container">
    <h1>Report di Inferenza SVG Captioning - Nuovi Esempi</h1>
    <p style="text-align: center;">Report generato il 04/05/2025 15:45:30</p>
    <div style="max-width: 800px; margin: 20px auto; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #4285f4;">
        <h3 style="margin-top: 0; color: #4285f4;">Informazioni sul Report</h3>
        <p>Questo report contiene esempi di inferenza per i modelli Llama 3.1 8B e Gemma 2 9B IT su immagini SVG di diverse complessità.</p>
        <p>Per ogni esempio sono riportate:</p>
        <ul>
            <li>L'immagine SVG</li>
            <li>La caption ground truth</li>
            <li>La caption generata dal modello</li>
            <li>Il tempo di inferenza in secondi</li>
        </ul>
        <p>Gli esempi sono organizzati per complessità (simple, medium) e per modello.</p>
    </div>

    <div class="complexity-section simple-section">
        <h2>Complessità: Simple</h2>

        <div class="model-section">
            <h3>Llama 3.1 8B (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_simple_new_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a bookmark with a rectangular shape
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> Un'immagine di un quadrato con una diagonale diagonale in un colore marrone scuro e bordi bianchi, con un'opacità di 1.

Risposta:
Un'immagine di un quadrato con una diagonale diagonale in un colore marrone scuro e bordi bianchi, con un'opacità di 1. 
La diagonale è un segmento di retta che congiunge due angoli opposti di un poligono, in questo caso un quadrato. 
Il colore del quadrato è marrone scuro, e i bordi sono bianchi. 
L'opacità è 1, il che significa che l'
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 18.38 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_simple_new_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, iconic symbol of a circular arrow forming a loop
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine rappresenta un logo o un simbolo con due forme geometriche a forma di cuore, una più grande e una più piccola, entrambe con una struttura composta da linee rette e curve che formano un pattern. La forma più grande è posizionata nella parte superiore della pagina, mentre la forma più piccola è posizionata nella parte inferiore. Entrambe le forme hanno una struttura simile, con una forma principale a cuore e due linee che si estendono verso l'esterno, formando una sorta di "ala" o di "coda" che si estende lateralmente. La strutt
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.52 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_simple_new_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a red diamond shape with a unique design
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> Immagine di un cuore rosso con un diametro di 256 punti, che si trova all'interno di un rettangolo più grande con i vertici al centro dei quattro punti cardinali. 

Risposte:
A) L'immagine rappresenta un cuore rosso all'interno di un rettangolo più grande.
B) L'immagine rappresenta un cuore rosso con un diametro di 256 punti.
C) L'immagine rappresenta un cuore rosso con un rettangolo più grande con i vertici al centro dei quattro punti cardinali.
D) L'immagine rappresenta un ret
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.28 secondi
                </div>
            </div>
        </div>

        <div class="model-section">
            <h3>Gemma 2 9B IT (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_simple_new_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a bookmark with a rectangular shape
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un semplice poligono di quattro lati con un colore di riempimento aranciato.

* **Dimensione:** L'immagine ha una dimensione di 512x512 pixel, come definito nell'attributo `viewBox`.

* **Forma:** La forma è un trapezio, definita dalla proprietà `d` dell'elemento `path`. 

* **Colore:** Il riempimento del trapezio è di colore aranciato, definito dall'attributo `fill` come `241,186,118`, che rappresenta i valori RGB. Il colore di contorno è assente (`stroke:None`).

* **Spessore del contorno:**
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 17.11 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_simple_new_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, iconic symbol of a circular arrow forming a loop
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta due figure geometriche sovrapposte.

**Figura 1:**

* Forma: Una sorta di "C" rovesciata con una piccola protuberanza sul lato destro. 
* Colore: Un verde scuro, con un codice di colore hex #836541.
* Posizione: Occupando la parte sinistra dell'immagine.

**Figura 2:**

* Forma: Una "U" rovesciata con una piccola protuberanza sul lato sinistro.
* Colore: Stesso verde scuro del primo figura.
* Posizione: Occupando la parte destra dell'immagine.

Le due figure si sovrappongono leggermente nella zona centrale, creando un
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.45 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_simple_new_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a red diamond shape with a unique design
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta due forme geometriche intersecate. 

* **Forma 1:**  Un triangolo con vertici in (381,381), (256,226) e (256,293). I lati del triangolo sono spessi 10 pixel e di colore rosso (#175,0,0). La forma è riempita con trasparenza (fill:none), quindi è visibile solo il contorno.

* **Forma 2:** Un quadrato con vertici in (6,256), (256,6), (506,256) e (256,50
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.10 secondi
                </div>
            </div>
        </div>
    </div>

    <div class="complexity-section medium-section">
        <h2>Complessità: Medium</h2>

        <div class="model-section">
            <h3>Llama 3.1 8B (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_medium_new_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, abstract symbol that resembles a stylized, upright, and elongated cup or goblet
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> Questa immagine è composta da un gruppo di linee e forme geometriche che rappresentano un modello di carta da parati. Il modello è composto da un fondo rettangolare con una serie di forme geometriche disposte in modo casuale. Le forme geometriche sono rappresentate da linee e curve che formano una disposizione simmetrica e ripetitiva.

Analisi: Questa immagine è composta da una serie di elementi geometrici che formano una disposizione simmetrica e ripetitiva. Le forme geometriche sono disposte in modo casuale, ma sembrano seguire una disposizione simmetrica. L'immagine
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.38 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_medium_new_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, two-legged ladder
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine rappresenta un quadrato con quattro rette diagonali che attraversano il centro del quadrato. Ciascuna diagonale è composta da tre rette, formando un triangolo equilatero. Il quadrato è centrato e ha un colore grigio scuro. Le rette sono di colore grigio scuro e hanno una larghezza di 1 unità. Il quadrato ha un'altezza di 512 unità e un'altezza di 512 unità. L'immagine è stata creata utilizzando il software di editing grafico Adobe Illustrator.

Risposta: L'immagine rappresenta un quadrato con quattro ret
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.53 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_medium_new_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple graphic design featuring a red circle with a black border
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> Questa immagine rappresenta un simbolo astratto di un cuore con due frecce all'ingresso e all'uscita. Il simbolo è composto da tre parti principali: un cuore centrale, due frecce che si dirigono verso il cuore, e un cerchio che rappresenta il cuore. Il cuore è un cerchio con un'ampia curva al centro. Le frecce sono due rette che si estendono dal cerchio, una verso destra e l'altra verso sinistra. Il colore del simbolo è principalmente grigio scuro, con due frecce che si distinguono per il loro colore rosso acceso.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.59 secondi
                </div>
            </div>
        </div>

        <div class="model-section">
            <h3>Gemma 2 9B IT (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_medium_new_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, abstract symbol that resembles a stylized, upright, and elongated cup or goblet
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un'aquila, vista frontalmente.

* **Corpo:** Il corpo dell'aquila è composto da un singolo percorso, con linee curve che delineano la forma del corpo, le ali e la testa.
* **Colori:** L'aquila è nera. 
* **Dettagli:**
    * **Testa:** La testa è piccola rispetto al corpo, con un becco appuntito.
    * **Ali:** Le ali sono larghe e aperte, con le penne stilizzate.
    * **Piedi:** I piedi sono visibili solo come linee, che si estendono verso il basso.

**Osservazioni:**

* L
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.46 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_medium_new_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, two-legged ladder
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un disegno geometrico con linee nere. 

**Struttura:**

L'immagine è composta da 7 linee che si intersecano e formano una figura complessiva. 

**Dettagli:**

* **Linea 1:** Si estende orizzontalmente dal punto (256, 420) al punto (94, 420), creando un segmento di linea.
* **Linea 2:** Si estende orizzontalmente dal punto (284, 318) al punto (120, 318), creando un altro segmento di linea.
* **Linea 3:** Si estende orizzontalmente dal punto (310, 216) al punto (154, 216).
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.62 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_medium_new_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple graphic design featuring a red circle with a black border
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un semplice design geometrico.

**Elementi:**

* **Path 1:**
    * Stile: Riempimento grigio (#B3B3B3), bordo nero (#000000), spessore del bordo di 25px, opacità 1.
    * Forma: Triangolo con base a sinistra e punta in alto. 
    * Posizione: In alto a sinistra.
* **Path 2:**
    * Stile: Riempimento trasparente, bordo rosso (#F3717A), spessore del bordo di 40px, opacità 1.
    * Forma: Triangolo con base a destra.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.48 secondi
                </div>
            </div>
        </div>
    </div>

    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente.</p>
        <p>© 2025 Università di Modena e Reggio Emilia</p>
    </footer>
    </div>
</body>
</html>
