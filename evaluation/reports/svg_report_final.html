<!DOCTYPE html>
<html>
<head>
    <title>Report di Inferenza SVG Captioning</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
            text-align: center;
        }
        h2 {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #ddd;
        }
        h3 {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            color: #555;
        }
        .svg-placeholder {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin: 0 auto;
            background-color: #f9f9f9;
        }
        .example {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .caption {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .ground-truth {
            color: #2c7fb8;
        }
        .generated {
            color: #31a354;
        }
        .model-info {
            color: #636363;
            font-style: italic;
            margin-top: 5px;
        }
        .metrics {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .complexity-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #fafafa;
            border-radius: 5px;
            border-left: 5px solid #ddd;
        }
        .simple-section {
            border-left-color: #a1d99b;
        }
        .medium-section {
            border-left-color: #fdae6b;
        }
    </style>
</head>
<body>
    <div class="container">
    <h1>Report di Inferenza SVG Captioning</h1>
    <p style="text-align: center;">Report generato il 04/05/2025 14:30:45</p>
    <div style="max-width: 800px; margin: 20px auto; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #4285f4;">
        <h3 style="margin-top: 0; color: #4285f4;">Informazioni sul Report</h3>
        <p>Questo report contiene esempi di inferenza per i modelli Llama 3.1 8B e Gemma 2 9B IT su immagini SVG di diverse complessità.</p>
        <p>Per ogni esempio sono riportate:</p>
        <ul>
            <li>L'immagine SVG</li>
            <li>La caption ground truth</li>
            <li>La caption generata dal modello</li>
            <li>Il tempo di inferenza in secondi</li>
        </ul>
        <p>Gli esempi sono organizzati per complessità (simple, medium) e per modello.</p>
    </div>

    <div class="complexity-section simple-section">
        <h2>Complessità: Simple</h2>

        <div class="model-section">
            <h3>Llama 3.1 8B (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_simple_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a black arrow pointing upwards
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> Immagine di un uomo con la testa di un leone e la parte inferiore del corpo di un uomo.
Dettagli: 
- Il personaggio ha la testa di un leone con la bocca aperta e gli occhi che guardano verso l'alto.
- La testa è rivolta verso l'alto e il collo è lungo.
- La parte inferiore del corpo è quella di un uomo, con le gambe e le braccia.
- Il personaggio è posato con una gamba dietro l'altra e il peso del corpo è appoggiato sulla gamba anteriore.
- L'immagine è rappresentata in modo bidimensionale.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.32 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_simple_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, minimalist design of a pair of earrings
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> Questa immagine è un'immagine vettoriale di un personaggio femminile stilizzato. L'immagine è composta da una serie di curve e linee che rappresentano il corpo e la testa della figura. La testa è rappresentata da un cerchio con un diametro leggermente inferiore rispetto al corpo, mentre il corpo è rappresentato da una serie di curve e linee che si incurvano verso l'alto e verso il basso. La figura è stilizzata e sembra essere in movimento, con le linee e le curve che si incurvano in modo fluido. L'immagine ha un aspetto dinamico e semplice.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.36 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_simple_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a stylized graphic consisting of two large, overlapping squares
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> questa immagine rappresenta un oggetto geometrico con forme curve, non è chiaro se si tratta di un oggetto astratto o una rappresentazione di un oggetto reale. Le forme curve sono disposte in modo da formare un oggetto con angoli smussati. Non sono presenti elementi di dettaglio o di superficie, il colore è un grigio chiaro.
Risposte possibili: 
- una sfera, una palla o un oggetto simile
- una rappresentazione di un oggetto astratto o di una forma geometrica
- un oggetto di design o di arte, come un oggetto ornamentale o decorativo
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.20 secondi
                </div>
            </div>
        </div>

        <div class="model-section">
            <h3>Gemma 2 9B IT (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_simple_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a black arrow pointing upwards
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un semplice disegno di un tetto a due falde e un edificio.

* **Tetto:** Il primo elemento (`<path>`) disegna il tetto, con una forma triangolare. La base del tetto si estende su tutta la larghezza dell'immagine (512px), mentre l'apice si trova al centro. Il tetto è nero.
* **Edificio:** Il secondo elemento (`<path>`) rappresenta l'edificio. 
    * La parte inferiore dell'edificio è un rettangolo con le estremità ricurve, che si estende verticalmente dalla base del tetto fino a circa la metà dell'altezza dell'immagine. 
    * La parte superiore dell'edificio è un rettangolo più piccolo.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 10.99 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_simple_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, minimalist design of a pair of earrings
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un'unica forma, che assomiglia a una **S** stilizzata o a un **7** capovolto. 

**Dettagli:**

* **Colore:** Il contorno è nero, mentre l'interno è trasparente.
* **Stile:** Il contorno è realizzato con una linea spessa di 1 pixel.
* **Forma:** La forma è composta da una serie di curve e linee dritte che si intersecano. Si compone principalmente di due parti distinte:
    * Una curva che parte dall'alto a destra e scende verso sinistra, formando la parte superiore della "S" o del "7".
    * Una curva che parte dal basso a sinistra e sale verso destra.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.15 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_simple_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a stylized graphic consisting of two large, overlapping squares
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'SVG rappresenta un'immagine stilizzata di un'aquila.

* **Forma:** L'aquila è realizzata con una singola linea continua che forma un corpo arcuato e due ali estese. 
* **Posizione:** L'aquila è posizionata al centro della vista, con il corpo rivolto verso destra.
* **Colori:** L'aquila è realizzata con un contorno nero (stroke:187,187,187) e un'area interna vuota.

**Dettagli:**

* **Aletta:** Le ali dell'aquila sono larghe e leggermente curve verso l'alto.
* **Testa:** La testa dell'aquila non è chiaramente definita.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 10.82 secondi
                </div>
            </div>
        </div>
    </div>

    <div class="complexity-section medium-section">
        <h2>Complessità: Medium</h2>

        <div class="model-section">
            <h3>Llama 3.1 8B (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_medium_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, two-legged ladder
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine rappresenta un quadrato con quattro rette diagonali che attraversano il centro del quadrato. Ciascuna diagonale è composta da tre rette, formando un triangolo equilatero. Il quadrato è centrato e ha un colore grigio scuro. Le rette sono di colore grigio scuro e hanno una larghezza di 1 unità. Il quadrato ha un'altezza di 512 unità e un'altezza di 512 unità. L'immagine è stata creata utilizzando il software di editing grafico Adobe Illustrator.

Risposta: L'immagine rappresenta un quadrato con quattro rette diagonali.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.53 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_medium_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a single uppercase letter "A" in a bold, sans-serif font
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> l'immagine raffigura un computer classico con due finestre sullo schermo e un mouse. La descrizione della scena include:

*   Un computer classico con due finestre sullo schermo, descritte come "due finestre sullo schermo".
*   Un mouse, descritto come "un mouse".
*   Lo schermo del computer, descritto come "lo schermo".
*   L'interfaccia utente del computer, descritta come "l'interfaccia utente".
*   I contenuti delle finestre, descritti come "i contenuti delle finestre".
*   L'aspetto generale del computer, descritto come "l'aspetto generale del computer".
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.52 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/llama_medium_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple graphic design featuring a red circle with a black border
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> Questa immagine rappresenta un simbolo astratto di un cuore con due frecce all'ingresso e all'uscita. Il simbolo è composto da tre parti principali: un cuore centrale, due frecce che si dirigono verso il cuore, e un cerchio che rappresenta il cuore. Il cuore è un cerchio con un'ampia curva al centro. Le frecce sono due rette che si estendono dal cerchio, una verso destra e l'altra verso sinistra. Il colore del simbolo è principalmente grigio scuro, con due frecce che si distinguono per il loro colore rosso acceso.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 6.59 secondi
                </div>
            </div>
        </div>

        <div class="model-section">
            <h3>Gemma 2 9B IT (Zero-Shot)</h3>

            <div class="example">
                <h3>Esempio 1</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_medium_1.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple, two-legged ladder
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un disegno geometrico con linee nere. 

**Struttura:**

L'immagine è composta da 7 linee che si intersecano e formano una figura complessiva. 

**Dettagli:**

* **Linea 1:** Si estende orizzontalmente dal punto (256, 420) al punto (94, 420), creando un segmento di linea.
* **Linea 2:** Si estende orizzontalmente dal punto (284, 318) al punto (120, 318), creando un altro segmento di linea.
* **Linea 3:** Si estende orizzontalmente dal punto (310, 216) al punto (154, 216).
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.62 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_medium_2.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a single uppercase letter "A" in a bold, sans-serif font
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un quadrato con bordi spessi e un interno bianco. All'interno del quadrato, c'è una forma complessa nera che ricorda un uccello in volo, con ali spiegate e testa rivolta verso destra. 

**Dettagli:**

* **Quadrato esterno:** 
    * Ha dimensioni di 512x512 pixel.
    * È riempito di colore nero (`fill:0,0,0`).
    * Il contorno è sottile (`stroke-width:1`).
    * Le sue linee sono ben definite e chiuse (`d` nel tag `<path>`).

* **Quadrato interno bianco:**
    * Si trova all'interno del quadrato esterno.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.38 secondi
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3</h3>
                <div class="svg-placeholder">
                    <img src="svg_images/gemma_medium_3.svg" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> The image depicts a simple graphic design featuring a red circle with a black border
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> L'immagine SVG rappresenta un semplice design geometrico.

**Elementi:**

* **Path 1:**
    * Stile: Riempimento grigio (#B3B3B3), bordo nero (#000000), spessore del bordo di 25px, opacità 1.
    * Forma: Triangolo con base a sinistra e punta in alto. 
    * Posizione: In alto a sinistra.
* **Path 2:**
    * Stile: Riempimento trasparente, bordo rosso (#F3717A), spessore del bordo di 40px, opacità 1.
    * Forma: Triangolo con base a destra.
                </div>
                <div class="metrics">
                    <strong>Tempo di inferenza:</strong> 11.48 secondi
                </div>
            </div>
        </div>
    </div>

    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente.</p>
        <p>© 2025 Università di Modena e Reggio Emilia</p>
    </footer>
    </div>
</body>
</html>
