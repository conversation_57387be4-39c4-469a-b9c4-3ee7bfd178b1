# Report Didascalie SVG

Report generato il 03/05/2025 21:32:51

## Metriche di Valutazione

| Modello | BLEU-1 | BLEU-2 | BLEU-3 | BLEU-4 | METEOR | CIDEr | Tempo Medio (s) |
|---------|--------|--------|--------|--------|--------|-------|----------------|
| Llama 3.1 8B | 0.0150 | 0.0004 | 0.0001 | 0.0001 | 0.0345 | 0.7434 | 2.50 |
| Gemma 2 9B IT | 0.0114 | 0.0003 | 0.0001 | 0.0001 | 0.0291 | 0.8066 | 2.50 |

## Llama 3.1 8B

### Esempio 1

**File:** sample_40.svg

**Ground Truth:** sample_40.svg

**Generated:** **Descrizione dell'immagine:**

L'immagine rappresenta un simbolo composto da un cerchio con un punto al centro, circondato da un cerchio più grande con un punto al centro, entrambi con linee che li connettono a un cerchio più grande ancora, che a sua volta è circondato da un cerchio ancora più grande con un punto al centro. I cerchi più piccoli sono colorati di nero e hanno un colore di riempimento nullo, mentre il cerchio più grande è vuoto. La linea che unisce i cerchi più piccoli al cerchio più grande è una linea di riempimento nullo. I punti al centro dei cerchi più piccoli sono rappresentati da piccoli cerchi neri.

**Analisi dell'immagine:**

L'immagine è composta da tre cerchi concentrici, ognuno con un punto al centro. I cerchi più piccoli sono disegnati utilizzando il comando `A` dell'SVG, che disegna un arco di cerchio. I cerchi più grandi sono disegnati utilizzando il comando `L`

*Tempo di Inferenza: 339.6110s*

---

### Esempio 2

**File:** sample_50.svg

**Ground Truth:** sample_50.svg

**Generated:** La tua risposta finale sarà un elenco di dettagli specifici che descrivono l’immagine SVG. Ecco alcuni esempi di dettagli che potresti includere:
- La presenza di un cerchio completo
- La presenza di una serie di cerchi
- La presenza di una linea orizzontale
- La presenza di una linea verticale
- La presenza di un rettangolo
- La presenza di un triangolo
- La presenza di un quadrato
- La presenza di un poligono
- La presenza di una forma a forma di cuore
- La presenza di una stella
- La presenza di una figura geometrica complessa
- La presenza di una linea curva
- La presenza di una curva di Bézier
- La presenza di una curva di Catmull-Rom
- La presenza di una curva di Hermite
- La presenza di una curva di Lagrange
- La presenza di una curva di Spline
- La presenza di una curva di Steiner
- La presenza di una curva di B

*Tempo di Inferenza: 310.9573s*

---

### Esempio 3

**File:** sample_8.svg

**Ground Truth:** sample_8.svg

**Generated:** La risposta finale sarà:
Il file SVG contiene un solo elemento: un percorso che rappresenta un'immagine composta da 8 segmenti di retta. La sua funzione è rappresentare un'immagine composta da 8 segmenti di retta. 
La risposta finale è:
Il file SVG contiene un solo elemento: un percorso che rappresenta un'immagine composta da 8 segmenti di retta. La sua funzione è rappresentare un'immagine composta da 8 segmenti di retta.  La risposta finale è:
Il file SVG contiene un solo elemento: un percorso che rappresenta un'immagine composta da 8 segmenti di retta. La sua funzione è rappresentare un'immagine composta da 8 segmenti di retta. 
La risposta finale è:
Il file SVG contiene un solo elemento: un percorso che rappresenta un'immagine composta da 8 segmenti di retta. La sua funzione è rappresentare un'immagine composta da 8 segmenti di retta. 
La risposta finale è:
Il file SVG contiene un solo

*Tempo di Inferenza: 313.1153s*

---

### Esempio 4

**File:** sample_13.svg

**Ground Truth:** sample_13.svg

**Generated:** Queste informazioni di base sono state estratte dall'immagine SVG:
- **Dimensioni**: 512 x 512
- **Colore**: Nero
- **Stile**: Linea
- **Intersezione**: Nessuna
- **Stile di riempimento**: Nessuno
- **Tipo di forma**: Curva
- **Intersezione**: Nessuna
- **Intersezione**: Nessuna

Questo SVG rappresenta un'immagine di un oggetto geometrico con una forma complessa. La forma è composta da una serie di curve che formano un cerchio, ma non è un cerchio perfetto. La forma è composta da 5 curve, con un angolo di 90 gradi tra di loro. Le curve sono tutte arcuate, con un raggio di curvatura variabile.

L'immagine può essere utilizzata come icona o come elemento grafico in un layout web. La sua forma complessa e il colore nero la rendono un elemento visivamente interessante e può essere utilizzata per catturare l'attenzione dell'utente.

La forma può essere utilizzata in vari contesti, ad

*Tempo di Inferenza: 312.7632s*

---

### Esempio 5

**File:** sample_18.svg

**Ground Truth:** sample_18.svg

**Generated:** Questo codice SVG rappresenta una icona di un gatto. La prima parte del codice definisce il rettangolo nero che rappresenta il corpo del gatto, mentre la seconda parte definisce le linee che rappresentano la testa, le orecchie e la coda del gatto.

In particolare, la prima parte del codice definisce un rettangolo con coordinate (0,473) - (512,473), (512,505) - (0,505) e (0,473) - (0,505). Questo rettangolo rappresenta il corpo del gatto.

La seconda parte del codice definisce una serie di linee che rappresentano la testa, le orecchie e la coda del gatto. Queste linee sono definite da un insieme di punti (x,y) che rappresentano le coordinate delle linee.

In particolare, la seconda parte del codice definisce le seguenti linee:

- Una linea che parte da (240,68) e arriva a (240,426)
- Una linea che parte da (272,426) e arriva a (

*Tempo di Inferenza: 312.7104s*

---

## Gemma 2 9B IT

### Esempio 1

**File:** sample_40.svg

**Ground Truth:** sample_40.svg

**Generated:** Questa immagine SVG rappresenta un **uccello stilizzato**, probabilmente un **passero**, con le seguenti caratteristiche:

* **Testa:** La testa è leggermente arrotondata, con un piccolo becco all'estremità inferiore.
* **Corpo:** Il corpo è ovale e leggermente appuntito verso il basso.
* **Ali:** Le ali sono rappresentate da due curve che partono dal corpo e si estendono verso l'esterno.
* **Coda:** La coda è corta e appuntita.
* **Zampe:** Le zampe non sono visibili nell'immagine.
* **Colori:** L'uccello è nero, con un'area bianca sotto il corpo.

**Dettagli tecnici:**

* La dimensione dell'immagine è di 512x512 pixel.
* L'immagine è stata creata usando il linguaggio SVG (Scalable Vector Graphics).
* Il colore dell'uccello è nero, mentre l'area bianca sotto il corpo è definita come "trasparente" nell'attributo `fill`.

**Interpretazione:**

L'immagine rappresenta un uccello in volo, con le ali spiegate e la coda leggermente sollevata. La sua

*Tempo di Inferenza: 261.7223s*

---

### Esempio 2

**File:** sample_50.svg

**Ground Truth:** sample_50.svg

**Generated:** L'immagine SVG rappresenta un **grande quadrato diviso in nove quadrati più piccoli**. 

* **Contorno:**  Il quadrato è delineato da un contorno spessore 1px, riempito con il colore corrente.
* **Divisione:**  Il quadrato è diviso in nove quadrati di dimensioni uguali, disposti in tre righe e tre colonne.  
* **Struttura:** I quadrati interni sono organizzati in una struttura che ricorda un **gioco a scacchi**. 
* **Spazi vuoti:**  Il centro del quadrato è vuoto.

**Note:**

*  La dimensione dell'immagine è di 512x512 pixel.
*  Il colore del riempimento è definito come "currentColor", il che significa che il colore verrà ereditato dal contesto in cui l'immagine è inserita.


Potrebbe essere utilizzato come:

* **Icona:** Simbolicamente potrebbe rappresentare una griglia, una matrice o un gioco a scacchi.
* **Elemento di design:** Potrebbe essere utilizzato come base per un pattern o un design più complesso.

*Tempo di Inferenza: 250.0483s*

---

### Esempio 3

**File:** sample_8.svg

**Ground Truth:** sample_8.svg

**Generated:** L'immagine SVG rappresenta un percorso geometrico con linee rette e curve. 

**Dettagli:**

* **Dimensioni:** L'immagine ha una dimensione di 512x512 pixel, definita dall'attributo `viewBox`.
* **Colore di riempimento:** Il percorso è trasparente (`fill:0,0,0`).
* **Colore del bordo:** Il percorso ha un bordo grigio chiaro (`stroke:187,187,187`).
* **Spessore del bordo:** Lo spessore del bordo è di 15 pixel (`stroke-width:15`).
* **Opacità:** L'opacità del bordo è 1 (completamente opaco).
* **Forma:** Il percorso forma una figura complessa con linee rette e curve che ricordano una combinazione di una "Y" e di un "M". 

**Posizionamento:**

* Il percorso è posizionato al centro dell'area di visualizzazione, definita da `viewBox="0 0 512 512"`.

**Analisi:**

L'immagine SVG è piuttosto astratta e la sua interpretazione dipende dalla soggettività di chi la osserva. La forma

*Tempo di Inferenza: 260.2841s*

---

### Esempio 4

**File:** sample_13.svg

**Ground Truth:** sample_13.svg

**Generated:** L'SVG rappresenta una figura geometrica complessa che assomiglia ad un'ala di uccello o ad un'impronta. 

**Dettagli:**

* **Dimensioni:** La figura occupa tutto lo spazio all'interno del viewport di 512x512 pixel.
* **Colore:** La figura è nera, con un'ampiezza del tratto di 1 pixel.
* **Stile:** La figura è riempita con un colore trasparente (fill:none), quindi solo il contorno è visibile.
* **Forma:** La forma è composta da curve e segmenti di linea che si intersecano in diversi punti. 
* **Simmetria:** La figura non presenta una simmetria evidente.
* **Elementi:** La figura è composta da un unico elemento `<path>`, che definisce il percorso della linea che forma l'immagine.
* **Attributo d:** L'attributo `d` del tag `<path>` contiene la descrizione matematica del percorso della linea.

**Interpretazione:**

La forma dell'SVG potrebbe essere interpretata in diversi modi, a seconda della prospettiva individuale. 

* **Ala di uccello:** La forma curva e appuntita potrebbe ricordare

*Tempo di Inferenza: 260.7909s*

---

### Esempio 5

**File:** sample_18.svg

**Ground Truth:** sample_18.svg

**Generated:** L'immagine SVG rappresenta un'aquila stilizzata in volo.

* **Corpo:** Il corpo dell'aquila è formato da un triangolo rettangolo in basso, che si estende dal punto di origine (0,0) fino a 512x505. 
* **Ali:** Le ali sono rappresentate da un'altra forma triangolare, posizionata al centro dell'immagine. Questa forma ha una base più corta rispetto al corpo, e si estende verso l'alto, con un angolo acuto che simula il movimento dell'apertura delle ali.
* **Testa:** La testa dell'aquila non è esplicitamente rappresentata, ma si può intuire dalla forma dell'ala superiore, che sembra inclinata verso il basso come se l'aquila stesse guardando verso il basso.
* **Dettagli:** L'immagine non presenta dettagli specifici come occhi, becco o piume. 

In generale, l'aquila è rappresentata in modo semplificato e stilizzato, con linee nette e forme geometriche. Il bianco dello sfondo contrasta con il nero dell'aquila, rendendo la figura visivamente accattivante.

*Tempo di Inferenza: 258.5958s*

---


---

Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.

© 2025 Università di Modena e Reggio Emilia
