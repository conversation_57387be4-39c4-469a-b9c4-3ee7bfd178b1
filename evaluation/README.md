# Valutazione delle Didascalie

Questa directory contiene gli script e i risultati per la valutazione delle didascalie generate.

## Script Principali

- **evaluate_captions.py**: Script per la valutazione delle didascalie generate
- **visualize_results.py**: Script per la visualizzazione dei risultati dell'inferenza
- **compare_models.py**: Script per il confronto dei risultati di diversi modelli
- **evaluate_checkpoints.py**: Script per la valutazione dei checkpoint con metriche multiple
- **visualize_checkpoint_metrics.py**: Script per la visualizzazione dei risultati delle valutazioni dei checkpoint

## Struttura della Directory

- **metrics/**: Metriche di valutazione per i diversi modelli
- **reports/**: Report HTML con i risultati dell'inferenza
  - **complete_inference_report.html**: Report completo dell'inferenza
  - **exact_svg_report.html**: Report con SVG incorporati direttamente
  - **fixed_svg_report.html**: Report con SVG corretti
  - **svg_files_report.html**: Report con SVG come file separati
  - **inference_radar_chart.png**: Grafico radar delle metriche di inferenza
- **comparisons/**: Grafici e tabelle comparative
- **checkpoint_metrics/**: Metriche di valutazione per i checkpoint dei modelli
- **zero_shot/**: Risultati della valutazione zero-shot dei modelli base
  - **Llama-3.1-8B-Instruct_zero_shot_examples.json**: Esempi generati da Llama 3.1 8B
  - **gemma-2-9b-it_zero_shot_examples.json**: Esempi generati da Gemma 2 9B IT
  - **zero_shot_metrics.json**: Metriche aggregate per tutti i modelli zero-shot
  - **zero_shot_examples.json**: File combinato di tutti gli esempi zero-shot

## Metriche Implementate

### Metriche di Qualità

- **BLEU**: Bilingual Evaluation Understudy
  - BLEU-1: Precisione unigrammi
  - BLEU-2: Precisione bigrammi
  - BLEU-3: Precisione trigrammi
  - BLEU-4: Precisione quadrigrammi
- **ROUGE**: Recall-Oriented Understudy for Gisting Evaluation
  - ROUGE-1: Precisione/recall unigrammi
  - ROUGE-2: Precisione/recall bigrammi
  - ROUGE-L: Longest Common Subsequence
- **METEOR**: Metric for Evaluation of Translation with Explicit ORdering
- **CIDEr**: Consensus-based Image Description Evaluation
- **CLIP Score**: Misura di similarità tra immagine e testo usando CLIP

### Metriche di Efficienza

- **Tempo di Inferenza**: Tempo necessario per generare una didascalia
- **Perplexity**: Misura di confidenza del modello

### Metriche di Analisi Linguistica

- **Lunghezza delle Didascalie**: Statistiche sulla lunghezza delle didascalie generate
- **Diversità del Vocabolario**: Dimensione del vocabolario utilizzato
- **Type-Token Ratio**: Rapporto tra tipi e token (misura di diversità lessicale)
- **Self-BLEU**: Misura di diversità tra le didascalie generate

## Utilizzo

### Valutazione Zero-Shot

```bash
# Valutazione di un modello base in modalità zero-shot
sbatch experiments/xml_direct_input/run_evaluate_zero_shot.slurm \
    meta-llama/Llama-3.1-8B-Instruct \
    /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    /work/tesi_ediluzio/evaluation/zero_shot \
    "" \
    "--load_in_4bit" \
    100 \
    "--use_clip" \
    "337543-unimore" \
    "captioner_zero_shot"

# Generazione di un report HTML per la valutazione zero-shot
python experiments/xml_direct_input/generate_zero_shot_report.py \
    --output_dir /work/tesi_ediluzio/evaluation/reports \
    --metrics_file /work/tesi_ediluzio/evaluation/zero_shot/zero_shot_metrics.json \
    --examples_file /work/tesi_ediluzio/evaluation/zero_shot/zero_shot_examples.json \
    --title "Valutazione Zero-Shot dei Modelli di Generazione Didascalie SVG" \
    --num_examples 5
```

### Valutazione delle Didascalie

```bash
python evaluate_captions.py --results_file /path/to/results.jsonl --output_file /path/to/evaluation.json
```

### Visualizzazione dei Risultati

```bash
python visualize_results.py --results_file /path/to/results.jsonl --metrics_file /path/to/metrics.json --output_file /path/to/report.html
```

### Confronto dei Modelli

```bash
python compare_models.py --metrics_dir /path/to/metrics --output_dir /path/to/comparisons
```

### Valutazione dei Checkpoint

```bash
# Valutazione manuale di checkpoint specifici
sbatch experiments/xml_direct_input/run_checkpoint_evaluation.slurm \
    meta-llama/Llama-3.1-8B-Instruct \
    /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence \
    /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence \
    "" \
    "" \
    "--load_in_4bit" \
    100 \
    "--use_clip"

# Monitoraggio automatico dei checkpoint
bash experiments/xml_direct_input/run_checkpoint_monitor.sh \
    meta-llama/Llama-3.1-8B-Instruct \
    /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence \
    /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence \
    "" \
    "--load_in_4bit" \
    100 \
    "--use_clip" \
    1800 \
    200 \
    5

# Visualizzazione dei risultati delle valutazioni
python experiments/xml_direct_input/visualize_checkpoint_metrics.py \
    --metrics_dir /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence \
    --output_dir /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence/visualizations \
    --model_name "Llama 3.1 8B (Test1 Convergence)" \
    --use_wandb
```

## Formato dei Risultati

### Valutazione Standard

Il file di output della valutazione standard è un JSON con la seguente struttura:

```json
{
  "aggregated": {
    "bleu-1": 0.1234,
    "bleu-2": 0.1234,
    "bleu-3": 0.1234,
    "bleu-4": 0.1234,
    "rouge-1": 0.1234,
    "rouge-2": 0.1234,
    "rouge-l": 0.1234
  },
  "per_example": {
    "bleu-1": [0.1, 0.2, ...],
    "bleu-4": [0.1, 0.2, ...],
    "rouge-l": [0.1, 0.2, ...]
  }
}
```

### Valutazione dei Checkpoint

Il file di output della valutazione dei checkpoint è un JSON con la seguente struttura:

```json
{
  "step": 1000,
  "epoch": 2.5,
  "training_loss": 0.4567,
  "eval_loss": 0.5678,
  "bleu1": 0.1234,
  "bleu2": 0.1234,
  "bleu3": 0.1234,
  "bleu4": 0.1234,
  "meteor": 0.1234,
  "cider": 0.1234,
  "clip_score": 0.1234,
  "caption_length_mean": 45.67,
  "caption_length_std": 12.34,
  "caption_length_min": 20,
  "caption_length_max": 80,
  "vocabulary_size": 1234,
  "type_token_ratio": 0.1234,
  "self_bleu": 0.1234,
  "inference_time_mean": 1.234,
  "inference_time_std": 0.123,
  "inference_time_min": 0.5,
  "inference_time_max": 2.5,
  "inference_time_total": 123.4,
  "perplexity_mean": 12.34,
  "perplexity_std": 3.45,
  "perplexity_min": 5.67,
  "perplexity_max": 23.45,
  "num_samples": 100
}
```

## Workflow di Valutazione

### Valutazione Zero-Shot

1. **Esegui la valutazione zero-shot** con `run_evaluate_zero_shot.slurm`
2. **Calcola le metriche** per i modelli base
3. **Genera un report HTML** con `generate_zero_shot_report.py`
4. **Analizza i risultati** per stabilire una baseline di performance

### Valutazione Standard

1. **Esegui l'inferenza** con i modelli selezionati
2. **Valuta le didascalie** generate con `evaluate_captions.py`
3. **Visualizza i risultati** con `visualize_results.py`
4. **Confronta i modelli** con `compare_models.py`

### Valutazione dei Checkpoint

1. **Avvia il monitoraggio dei checkpoint** con `run_checkpoint_monitor.sh`
2. **Valuta automaticamente i nuovi checkpoint** durante il training
3. **Visualizza i risultati** con `visualize_checkpoint_metrics.py`
4. **Analizza le metriche** per identificare il miglior checkpoint

### Valutazione Completa

1. **Esegui la valutazione completa** con `run_complete_evaluation.sh`
2. **Genera un report HTML** con confronto tra modelli zero-shot e fine-tuned
3. **Analizza i risultati** per identificare i migliori modelli e approcci
4. **Documenta le best practices** identificate

### Visualizzazioni Disponibili

La valutazione dei checkpoint genera i seguenti grafici:

- **Loss**: Training loss vs. Validation loss
- **BLEU**: BLEU-1, BLEU-2, BLEU-3, BLEU-4
- **METEOR e CIDEr**: Andamento delle metriche METEOR e CIDEr
- **CLIP Score**: Andamento del CLIP Score
- **Statistiche Didascalie**: Lunghezza media, dimensione vocabolario, TTR
- **Tempo di Inferenza**: Tempo medio di inferenza
- **Perplexity**: Perplexity media
- **Metriche Normalizzate**: Confronto di tutte le metriche normalizzate
- **Radar Chart**: Visualizzazione radar del miglior checkpoint
- **Tabella Risultati**: Tabella comparativa di tutti i checkpoint

### Report di Inferenza

I report di inferenza generati includono:

- **complete_inference_report.html**: Report completo dell'inferenza con tutti i risultati
- **exact_svg_report.html**: Report con SVG incorporati direttamente nel file HTML
- **fixed_svg_report.html**: Report con SVG corretti per risolvere problemi di visualizzazione
- **svg_files_report.html**: Report con SVG salvati come file separati
- **inference_radar_chart.png**: Grafico radar che confronta le metriche di inferenza tra i modelli

Questi report mostrano:
- Le immagini SVG originali
- Le didascalie ground truth
- Le didascalie generate dai modelli
- I tempi di inferenza
- Confronti tra i modelli Llama 3.1 8B e Gemma 2 9B IT
