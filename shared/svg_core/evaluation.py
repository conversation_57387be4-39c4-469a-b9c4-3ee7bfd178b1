# decoder_only/svg_captioning/evaluation.py
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
from shared.utils import CaptionEvaluator
from shared.utils.logging_config import setup_logging

class DecoderOnlyEvaluator:
    """Classe dedicata alla valutazione del modello decoder-only"""
    
    def __init__(self, model_version: str = "default"):
        """
        Inizializza l'evaluator per decoder-only
        
        Args:
            model_version: Versione del modello per tracking
        """
        self.model_version = model_version
        self.logger = logging.getLogger(__name__)
        setup_logging(module_name=f"decoder_only_{model_version}")
        
        # Configurazione ottimizzata per decoder-only
        self.evaluator = CaptionEvaluator(
            n_grams=4,
            cache_size=50000  # Cache più grande per le lunghe sequenze tipiche di decoder-only
        )
        
        self.logger.info(f"Initialized DecoderOnlyEvaluator for version: {model_version}")

    def load_data(self, references_path: Path, candidates_path: Path) -> tuple:
        """Carica i dati di riferimento e i candidati"""
        self.logger.info(f"Loading data from {references_path} and {candidates_path}")
        
        try:
            with open(references_path, 'r') as f:
                references = json.load(f)
            
            with open(candidates_path, 'r') as f:
                candidates = json.load(f)
                
            assert len(references) == len(candidates), "Mismatch in references/candidates length"
            
            self.logger.info(f"Successfully loaded {len(references)} examples")
            return references, candidates
            
        except Exception as e:
            self.logger.error(f"Error loading data: {str(e)}")
            raise

    def evaluate(self, references: list, candidates: list) -> Dict[str, float]:
        """Esegue la valutazione completa"""
        self.logger.info("Starting evaluation process...")
        start_time = datetime.now()
        
        try:
            results = self.evaluator.evaluate_all(references, candidates)
            eval_time = (datetime.now() - start_time).total_seconds()
            
            self.logger.info(
                f"Evaluation completed in {eval_time:.2f}s\n"
                f"BLEU-4: {results['bleu4']:.4f}\n"
                f"ROUGE-L: {results['rougeL']:.4f}\n"
                f"CIDEr: {results['cider']:.4f}"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Evaluation failed: {str(e)}")
            raise

    def save_results(self, results: Dict[str, Any], output_dir: Path) -> Path:
        """Salva i risultati in formato JSON con metadati"""
        output_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = output_dir / f"eval_results_{timestamp}.json"
        
        result_data = {
            "model_type": "decoder_only",
            "model_version": self.model_version,
            "timestamp": datetime.now().isoformat(),
            "metrics": results,
            "config": {
                "n_grams": 4,
                "cache_size": 50000
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(result_data, f, indent=2)
        
        self.logger.info(f"Results saved to {output_path}")
        return output_path

def evaluate_decoder_only(
    references_path: Path,
    candidates_path: Path,
    output_dir: Path = Path("results/decoder_only"),
    model_version: str = "default"
) -> Dict[str, float]:
    """
    Pipeline completa di valutazione per decoder-only
    
    Args:
        references_path: Path al file JSON con i riferimenti
        candidates_path: Path al file JSON con le predizioni
        output_dir: Directory dove salvare i risultati
        model_version: Versione del modello
        
    Returns:
        Dizionario con i risultati delle metriche
    """
    evaluator = DecoderOnlyEvaluator(model_version=model_version)
    
    try:
        # Caricamento dati
        references, candidates = evaluator.load_data(references_path, candidates_path)
        
        # Valutazione
        results = evaluator.evaluate(references, candidates)
        
        # Salvataggio risultati
        evaluator.save_results(results, output_dir)
        
        return results
        
    except Exception as e:
        evaluator.logger.error(f"Evaluation pipeline failed: {str(e)}")
        raise

if __name__ == "__main__":
    # Configurazione di esempio
    CONFIG = {
        "references_path": Path("data/decoder_only/references.json"),
        "candidates_path": Path("results/decoder_only/candidates.json"),
        "output_dir": Path("results/decoder_only/evaluations"),
        "model_version": "v1.0"  # Versione specifica del modello
    }
    
    # Esecuzione della pipeline
    results = evaluate_decoder_only(**CONFIG)
    
    # Output dei risultati
    print("\nFinal Evaluation Results:")
    for metric, score in results.items():
        print(f"{metric.upper():<8}: {score:.4f}")
