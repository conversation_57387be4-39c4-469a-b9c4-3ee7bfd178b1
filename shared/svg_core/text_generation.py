"""
Text Generation Module for SVG Captioning

This module handles the generation of textual descriptions for SVG files:
1. Beam search implementation
2. Temperature and sampling control
3. Length control and formatting
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from transformers import GenerationConfig

class TextGenerator:
    """
    Class for generating textual descriptions from SVG representations.
    """
    
    def __init__(self, 
                 nemotron_model,
                 max_length: int = 100,
                 min_length: int = 10,
                 num_beams: int = 5,
                 temperature: float = 0.7,
                 top_p: float = 0.9,
                 top_k: int = 50,
                 no_repeat_ngram_size: int = 2,
                 length_penalty: float = 1.0):
        """
        Initialize the text generator.
        
        Args:
            nemotron_model: Instance of NemotronModel
            max_length: Maximum length of generated text
            min_length: Minimum length of generated text
            num_beams: Number of beams for beam search
            temperature: Temperature for sampling
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            no_repeat_ngram_size: Size of n-grams to avoid repeating
            length_penalty: Penalty for longer sequences
        """
        self.nemotron_model = nemotron_model
        
        # Create generation config
        self.generation_config = GenerationConfig(
            max_length=max_length,
            min_length=min_length,
            do_sample=True if temperature > 0 else False,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            num_beams=num_beams,
            no_repeat_ngram_size=no_repeat_ngram_size,
            length_penalty=length_penalty,
            pad_token_id=nemotron_model.tokenizer.pad_token_id,
            eos_token_id=nemotron_model.tokenizer.eos_token_id
        )
    
    def generate(self, tokenized_input: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """
        Generate a description for the tokenized SVG input.
        
        Args:
            tokenized_input: Dictionary with tokenized input
                - 'input_ids': Token IDs
                - 'attention_mask': Attention mask
            
        Returns:
            Dictionary with generation results:
            - 'caption': Generated caption
            - 'token_count': Number of tokens in the caption
            - 'generation_time': Time taken for generation (if available)
        """
        # Generate caption using the Nemotron model
        caption = self.nemotron_model.generate_caption(
            tokenized_input,
            generation_config=self.generation_config
        )
        
        # Post-process caption
        caption = self._post_process_caption(caption)
        
        # Count tokens in the caption
        token_count = len(self.nemotron_model.tokenizer.encode(caption))
        
        return {
            'caption': caption,
            'token_count': token_count,
            'generation_time': None  # Could be implemented with timing
        }
    
    def generate_multiple(self, 
                         tokenized_input: Dict[str, torch.Tensor],
                         num_captions: int = 3) -> List[Dict[str, Any]]:
        """
        Generate multiple descriptions for the tokenized SVG input.
        
        Args:
            tokenized_input: Dictionary with tokenized input
            num_captions: Number of captions to generate
            
        Returns:
            List of dictionaries with generation results
        """
        results = []
        
        # Create different generation configs for diversity
        for i in range(num_captions):
            # Adjust temperature and top_p for diversity
            temp_adjustment = 0.1 * i
            config = GenerationConfig(
                **self.generation_config.to_dict(),
                temperature=self.generation_config.temperature + temp_adjustment,
                top_p=min(0.95, self.generation_config.top_p + 0.05 * i)
            )
            
            # Generate caption
            caption = self.nemotron_model.generate_caption(
                tokenized_input,
                generation_config=config
            )
            
            # Post-process caption
            caption = self._post_process_caption(caption)
            
            # Count tokens
            token_count = len(self.nemotron_model.tokenizer.encode(caption))
            
            results.append({
                'caption': caption,
                'token_count': token_count,
                'generation_time': None,
                'config': {
                    'temperature': config.temperature,
                    'top_p': config.top_p
                }
            })
        
        return results
    
    def _post_process_caption(self, caption: str) -> str:
        """
        Post-process the generated caption.
        
        Args:
            caption: Raw generated caption
            
        Returns:
            Processed caption
        """
        # Remove any special tokens that might have been generated
        caption = caption.strip()
        
        # Ensure the caption starts with a capital letter
        if caption and len(caption) > 0:
            caption = caption[0].upper() + caption[1:]
        
        # Ensure the caption ends with a period if it doesn't already
        if caption and not caption.endswith(('.', '!', '?')):
            caption += '.'
        
        return caption
    
    def update_generation_config(self, **kwargs):
        """
        Update the generation configuration.
        
        Args:
            **kwargs: Configuration parameters to update
        """
        config_dict = self.generation_config.to_dict()
        config_dict.update(kwargs)
        self.generation_config = GenerationConfig(**config_dict)
        print("Generation configuration updated.")

# Example usage
if __name__ == "__main__":
    # This example requires the NemotronModel to be imported and initialized
    print("Example usage of TextGenerator:")
    print("1. Initialize NemotronModel")
    print("2. Create TextGenerator with the model")
    print("3. Generate captions for tokenized SVG input")
    print("\nFor a complete example, see the end-to-end pipeline implementation.")
