"""
Modello di reward per il fine-tuning DPO del modello SVG captioning.
Basato sull'approccio Self-Cap per valutare la qualità delle descrizioni generate.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import CLIPModel, CLIPProcessor
import logging

# Configurazione del logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class SelfCapRewardModel(nn.Module):
    """
    Modello di reward basato su CLIP per valutare la qualità delle descrizioni SVG.
    Utilizza la similarità tra le rappresentazioni dell'immagine e del testo per calcolare un punteggio di reward.
    """
    
    def __init__(self, clip_model_name="openai/clip-vit-base-patch32", device=None):
        """
        Inizializza il modello di reward.
        
        Args:
            clip_model_name (str): Nome del modello CLIP da utilizzare
            device (str, optional): Dispositivo su cui eseguire il modello (cuda/cpu)
        """
        super().__init__()
        
        # Determina il dispositivo
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Inizializzando SelfCapRewardModel con {clip_model_name} su {self.device}")
        
        # Inizializzazione del modello CLIP
        try:
            self.clip_model = CLIPModel.from_pretrained(clip_model_name)
            self.processor = CLIPProcessor.from_pretrained(clip_model_name)
            self.clip_model.to(self.device)
            logger.info(f"Modello CLIP caricato con successo")
        except Exception as e:
            logger.error(f"Errore nel caricamento del modello CLIP: {e}")
            raise
        
        # Reward head per trasformare la similarità in un punteggio di reward
        self.reward_head = nn.Sequential(
            nn.Linear(1, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 1)
        ).to(self.device)
        
        # Dimensione dell'embedding CLIP
        self.embedding_dim = self.clip_model.config.projection_dim
        
    def preprocess_images(self, images):
        """
        Preprocessa le immagini SVG per l'input al modello CLIP.
        
        Args:
            images: Immagini SVG (possono essere percorsi, PIL Images o tensori)
            
        Returns:
            Tensori preprocessati
        """
        try:
            inputs = self.processor(images=images, return_tensors="pt", padding=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items() if k != "text"}
            return inputs
        except Exception as e:
            logger.error(f"Errore nel preprocessing delle immagini: {e}")
            raise
    
    def preprocess_text(self, captions):
        """
        Preprocessa le descrizioni testuali per l'input al modello CLIP.
        
        Args:
            captions: Lista di descrizioni testuali
            
        Returns:
            Tensori preprocessati
        """
        try:
            inputs = self.processor(text=captions, return_tensors="pt", padding=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items() if k != "pixel_values"}
            return inputs
        except Exception as e:
            logger.error(f"Errore nel preprocessing del testo: {e}")
            raise
    
    def forward(self, images, captions):
        """
        Calcola il reward per le coppie immagine-descrizione.
        
        Args:
            images: Immagini SVG
            captions: Descrizioni testuali
            
        Returns:
            Tensor: Punteggi di reward
        """
        batch_size = len(captions)
        logger.debug(f"Processing batch of size {batch_size}")
        
        try:
            # Preprocessing
            image_inputs = self.preprocess_images(images)
            text_inputs = self.preprocess_text(captions)
            
            # Estrazione delle features
            with torch.no_grad():
                image_features = self.clip_model.get_image_features(**image_inputs)
                text_features = self.clip_model.get_text_features(**text_inputs)
            
            # Normalizzazione
            image_features = image_features / image_features.norm(dim=1, keepdim=True)
            text_features = text_features / text_features.norm(dim=1, keepdim=True)
            
            # Calcolo similarità
            similarity = torch.bmm(
                image_features.unsqueeze(1), 
                text_features.unsqueeze(2)
            ).squeeze()
            
            # Calcolo reward
            reward = self.reward_head(similarity.unsqueeze(-1))
            
            return reward
            
        except Exception as e:
            logger.error(f"Errore nel calcolo del reward: {e}")
            raise
    
    def compute_reward_pairs(self, images, chosen_captions, rejected_captions):
        """
        Calcola i reward per coppie di descrizioni (scelte e rifiutate).
        
        Args:
            images: Immagini SVG
            chosen_captions: Descrizioni preferite
            rejected_captions: Descrizioni rifiutate
            
        Returns:
            tuple: (chosen_rewards, rejected_rewards)
        """
        chosen_rewards = self.forward(images, chosen_captions)
        rejected_rewards = self.forward(images, rejected_captions)
        
        return chosen_rewards, rejected_rewards
    
    def save_pretrained(self, output_dir):
        """
        Salva il modello di reward.
        
        Args:
            output_dir (str): Directory di output
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # Salva il modello CLIP
        self.clip_model.save_pretrained(os.path.join(output_dir, "clip_model"))
        self.processor.save_pretrained(os.path.join(output_dir, "clip_processor"))
        
        # Salva il reward head
        torch.save(self.reward_head.state_dict(), os.path.join(output_dir, "reward_head.pt"))
        
        logger.info(f"Modello di reward salvato in {output_dir}")
    
    @classmethod
    def from_pretrained(cls, model_dir, device=None):
        """
        Carica un modello di reward pre-addestrato.
        
        Args:
            model_dir (str): Directory del modello
            device (str, optional): Dispositivo su cui caricare il modello
            
        Returns:
            SelfCapRewardModel: Modello caricato
        """
        import os
        
        # Carica il modello CLIP
        clip_model_path = os.path.join(model_dir, "clip_model")
        
        # Inizializza il modello
        model = cls(clip_model_name=clip_model_path, device=device)
        
        # Carica il reward head
        reward_head_path = os.path.join(model_dir, "reward_head.pt")
        model.reward_head.load_state_dict(torch.load(reward_head_path, map_location=model.device))
        
        logger.info(f"Modello di reward caricato da {model_dir}")
        return model


# Test del modello
if __name__ == "__main__":
    # Esempio di utilizzo
    from PIL import Image
    import requests
    from io import BytesIO
    
    # Carica un'immagine di esempio
    url = "http://images.cocodataset.org/val2017/000000039769.jpg"
    image = Image.open(BytesIO(requests.get(url).content))
    
    # Crea alcune descrizioni
    captions = ["a cat sitting on a couch", "a dog running in the park"]
    
    # Inizializza il modello
    model = SelfCapRewardModel()
    
    # Calcola il reward
    rewards = model.forward([image, image], captions)
    print(f"Rewards: {rewards}")
