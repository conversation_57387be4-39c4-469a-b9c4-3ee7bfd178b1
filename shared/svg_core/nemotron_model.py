"""
Nemotron Model Integration Module for SVG Captioning

Handles integration of Nemotron models (e.g., Nemotron-4 Mini 3B/Instruct):
1. Model initialization (device, precision, token handling)
2. Tokenizer update for custom SVG tokens
3. Caption generation logic
4. Saving/Loading model state
"""
import os
import torch
import logging
from transformers import AutoModelForCausalLM, AutoTokenizer, GenerationConfig
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class NemotronModel:
    """Integrates Nemotron models for SVG captioning."""

    def __init__(self,
                 model_name: str = "nvidia/Nemotron-Mini-4B-Instruct",
                 device: str = "cuda" if torch.cuda.is_available() else "cpu",
                 fp16: bool = False,
                 cache_dir: Optional[str] = None,
                 auth_token: Optional[str] = None):
        """
        Initialize the Nemotron model and base tokenizer.

        Args:
            model_name (str): HF name or path.
            device (str): Device ('cuda' or 'cpu').
            fp16 (bool): Use FP16 precision (only on CUDA).
            cache_dir (Optional[str]): Hugging Face cache directory.
            auth_token (Optional[str]): HF token (reads from env if None).
        """
        self.model_name = model_name
        self.device = device
        self.fp16 = fp16 and self.device == "cuda" # FP16 only on CUDA

        # Get token: argument -> environment -> default (CLI login)
        if auth_token is None:
            auth_token = os.environ.get("HUGGING_FACE_HUB_TOKEN")
            logger.info(f"HF Token: {'Found in env' if auth_token else 'Not found in env, trying default login'}")
        # `use_auth_token=True` (implicit if auth_token is None or True) handles CLI login
        hf_auth_token = auth_token if isinstance(auth_token, str) else None # Pass string token if provided
        use_auth_token_flag = True if hf_auth_token is None else None # Let HF handle default login if no token string

        logger.info(f"Loading base tokenizer for {model_name}...")
        try:
            # Load base tokenizer first (will be updated later with SVG tokens)
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                token=hf_auth_token, # Pass token string if available
                use_auth_token=use_auth_token_flag # Use CLI token if no string token
            )
        except Exception as e:
            logger.error(f"Failed to load base tokenizer for {model_name}: {e}", exc_info=True)
            raise

        logger.info(f"Loading Nemotron model {model_name} on {device}...")
        model_kwargs = {}
        if self.fp16:
            model_kwargs["torch_dtype"] = torch.float16
            logger.info("Using FP16 precision.")
        else:
            model_kwargs["torch_dtype"] = torch.float32

        try:
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                token=hf_auth_token,
                use_auth_token=use_auth_token_flag,
                **model_kwargs
            )
            self.model.to(device)
            logger.info(f"Model loaded successfully to {device} with dtype {self.model.dtype}.")
        except Exception as e:
            logger.error(f"Error loading model {model_name} with args {model_kwargs}: {e}", exc_info=True)
            logger.info("Attempting fallback (FP32, low_cpu_mem_usage)...")
            try:
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_name, cache_dir=cache_dir, token=hf_auth_token, use_auth_token=use_auth_token_flag,
                    low_cpu_mem_usage=True, torch_dtype=torch.float32
                )
                self.model.to(device)
                logger.info(f"Model loaded with fallback configuration to {device}.")
            except Exception as fallback_e:
                logger.error(f"Fallback model loading also failed: {fallback_e}", exc_info=True)
                raise fallback_e

        self._ensure_pad_token()

        # Default generation config (can be overridden)
        self.default_gen_config = GenerationConfig(
            max_length=128, # Increased default max length
            min_length=10,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            top_k=50,
            num_beams=1, # Default to sampling (num_beams=1)
            no_repeat_ngram_size=2,
            pad_token_id=self.tokenizer.pad_token_id,
            eos_token_id=self.tokenizer.eos_token_id
        )
        logger.info(f"Default GenerationConfig: {self.default_gen_config}")

    def _ensure_pad_token(self):
        """Ensure the tokenizer has a pad token, setting it to EOS if missing."""
        if self.tokenizer.pad_token is None:
            if self.tokenizer.eos_token is not None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                # No need to resize model if just aliasing pad=eos
                # self.model.config.pad_token_id = self.model.config.eos_token_id
                logger.warning(f"Tokenizer missing pad_token, setting it to eos_token ({self.tokenizer.eos_token}).")
            else:
                # Add a new pad token if EOS is also missing (rare case)
                logger.warning("Tokenizer lacks both pad_token and eos_token. Adding a new pad_token '[PAD]'.")
                self.tokenizer.add_special_tokens({'pad_token': '[PAD]'})
                # IMPORTANT: Must resize embeddings if a *new* token is added
                self.resize_model_embeddings(len(self.tokenizer))
        # Ensure model config also knows the pad token ID
        if self.model.config.pad_token_id is None:
             self.model.config.pad_token_id = self.tokenizer.pad_token_id


    def resize_model_embeddings(self, new_num_tokens: int):
        """Resizes model embeddings to match the new tokenizer size."""
        try:
            self.model.resize_token_embeddings(new_num_tokens)
            # Also update the config
            self.model.config.vocab_size = new_num_tokens
            logger.info(f"Resized model token embeddings to {new_num_tokens}.")
        except Exception as e:
            logger.error(f"Failed to resize model embeddings: {e}", exc_info=True)
            raise

    def update_tokenizer(self, custom_tokenizer):
        """
        Updates the model's internal tokenizer and resizes embeddings if needed.

        Args:
            custom_tokenizer: An instance of the extended tokenizer (e.g., SVGDirectTokenizer).
                              Assumes it has a `.tokenizer` attribute holding the HF tokenizer instance
                              and potentially a `.get_vocabulary_size()` method.
        """
        hf_tokenizer = getattr(custom_tokenizer, 'tokenizer', custom_tokenizer)
        new_vocab_size = len(hf_tokenizer)
        current_vocab_size = getattr(self.model.config, 'vocab_size', -1)

        if new_vocab_size != current_vocab_size:
            logger.info(f"Updating model tokenizer. Current vocab size: {current_vocab_size}, New size: {new_vocab_size}")
            self.resize_model_embeddings(new_vocab_size)
        else:
            logger.info("Tokenizer vocabulary size matches model, no embedding resize needed.")

        self.tokenizer = hf_tokenizer
        self._ensure_pad_token() # Re-ensure pad token consistency
        logger.info("Model tokenizer reference updated.")


    @torch.no_grad() # Ensure inference mode for generation
    def generate_caption(self,
                         tokenized_input: Dict[str, torch.Tensor],
                         generation_config: Optional[GenerationConfig] = None) -> str:
        """
        Generate a caption for the tokenized input.

        Args:
            tokenized_input (Dict[str, torch.Tensor]): Dict with 'input_ids', 'attention_mask'.
                                                     Tensors should be on the correct device.
            generation_config (Optional[GenerationConfig]): Generation parameters. Uses default if None.

        Returns:
            str: The generated caption.
        """
        if not isinstance(tokenized_input, dict) or 'input_ids' not in tokenized_input:
             logger.error("Invalid tokenized_input format.")
             return ""

        # Use default generation config if none provided, ensure pad token ID is set
        gen_config = generation_config if generation_config is not None else self.default_gen_config
        if gen_config.pad_token_id is None:
            gen_config.pad_token_id = self.tokenizer.pad_token_id
        if gen_config.eos_token_id is None:
             gen_config.eos_token_id = self.tokenizer.eos_token_id


        input_ids = tokenized_input['input_ids'].to(self.device)
        attention_mask = tokenized_input.get('attention_mask') # Optional
        if attention_mask is not None:
            attention_mask = attention_mask.to(self.device)

        # Handle generation kwargs (pass directly to generate)
        gen_kwargs = gen_config.to_dict()

        logger.debug(f"Generating caption with config: {gen_kwargs}")
        logger.debug(f"Input IDs shape: {input_ids.shape}")

        try:
            # Generate output IDs
            output_ids = self.model.generate(
                input_ids=input_ids,
                attention_mask=attention_mask,
                **gen_kwargs
            )

            # Decode only the generated part (tokens after input_ids length)
            # Assumes output_ids contains the input_ids prefix
            input_length = input_ids.shape[1]
            generated_ids = output_ids[0, input_length:] # Get the first sequence, slice after input

            # Decode the generated part
            caption = self.tokenizer.decode(generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)
            logger.debug(f"Generated token IDs ({len(generated_ids)}): {generated_ids.tolist()}")
            logger.info(f"Generated caption: {caption}")

        except Exception as e:
             logger.error(f"Error during caption generation: {e}", exc_info=True)
             caption = "[GENERATION ERROR]"

        return caption.strip()

    def save_model(self, output_dir: str):
        """Saves the model and the *current* tokenizer state."""
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.tokenizer.save_pretrained(output_dir)
        logger.info(f"Model and current tokenizer saved to {output_dir}")

# Example Usage Block (optional)
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logger.info("Running NemotronModel example...")
    try:
        # Use a small public model for testing execution flow
        test_model_name = "gpt2"
        model_wrapper = NemotronModel(model_name=test_model_name, device="cpu")

        # Simulate updating with a dummy tokenizer (same vocab size for simplicity here)
        # In reality, you'd pass your SVGDirectTokenizer instance
        # model_wrapper.update_tokenizer(model_wrapper.tokenizer) # No resize needed

        # Prepare dummy input
        prompt = "This is an example prompt "
        dummy_input = model_wrapper.tokenizer(prompt, return_tensors="pt")

        # Generate
        generated_text = model_wrapper.generate_caption(dummy_input)
        print(f"Prompt: '{prompt}'")
        print(f"Generated Text: '{generated_text}'")

        # Example save
        # model_wrapper.save_model("./temp_gpt2_model")

    except Exception as main_e:
        logger.error(f"Error in NemotronModel example: {main_e}", exc_info=True)