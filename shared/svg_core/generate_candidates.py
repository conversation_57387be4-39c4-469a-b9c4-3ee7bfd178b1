# shared/svg_core/generate_candidates.py
"""
Script per la generazione di descrizioni candidate per il fine-tuning DPO.
Genera multiple descrizioni alternative per ogni SVG utilizzando diverse strategie di sampling.
"""

import torch
import logging
import numpy as np
from tqdm import tqdm
from typing import List, Dict, Any, Optional, Union

# Importa il processore dalla sua nuova posizione condivisa
from shared.svg_core.svg_vector_processor import SVGVectorProcessor # <<< CORREZIONE QUI

# Configurazione del logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class CandidateGenerator:
    """
    Generatore di descrizioni candidate per SVG.
    Utilizza diverse strategie di sampling per generare descrizioni diverse.
    """

    def __init__(self, model, tokenizer, device=None):
        """
        Inizializza il generatore di candidati.

        Args:
            model: Modello di linguaggio pre-addestrato
            tokenizer: Tokenizer associato al modello
            device (str, optional): Dispositivo su cui eseguire il modello (cuda/cpu)
        """
        self.model = model
        self.tokenizer = tokenizer
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Inizializzando CandidateGenerator su {self.device}")

        # Assicurati che il modello sia sul dispositivo corretto
        self.model.to(self.device)

    def generate_candidates(
        self,
        svg: str, # Accetta la stringa SVG o il percorso del file
        num_candidates: int = 16,
        max_length: int = 100,
        strategies: Optional[List[str]] = None,
        input_is_processed: bool = False, # Flag per indicare se l'input è già processato
        processed_input: Optional[Dict] = None # Per passare input già processato
    ) -> List[str]:
        """
        Genera multiple descrizioni candidate per un SVG.

        Args:
            svg (str): Codice SVG come stringa o percorso al file SVG.
            num_candidates (int): Numero di candidati da generare.
            max_length (int): Lunghezza massima delle descrizioni.
            strategies (List[str], optional): Strategie di sampling ('beam', 'nucleus', 'temperature', 'greedy').
            input_is_processed (bool): Se True, `processed_input` contiene dati già processati.
            processed_input (Optional[Dict]): Dati SVG già processati (richiede input_is_processed=True).

        Returns:
            List[str]: Lista di descrizioni candidate.
        """
        if strategies is None:
            strategies = ['nucleus', 'temperature', 'beam', 'greedy']

        logger.info(f"Generando {num_candidates} candidati per SVG usando {strategies}")

        # Prepara l'input: Tokenizza la rappresentazione testuale dell'SVG
        try:
            if input_is_processed and processed_input:
                # Se l'input è già processato (es. da un dataset), usalo direttamente
                # Assumiamo che il tokenizer sia SVGDirectTokenizer o simile
                # e che accetti i dati processati.
                 inputs = self.tokenizer.tokenize(processed_input) # Usa il metodo tokenize del wrapper
                 inputs = {k: v.to(self.device) for k, v in inputs.items()}
            elif isinstance(svg, str):
                # Se l'input è una stringa SVG o un percorso, processalo e tokenizzalo
                # Questo richiede che il tokenizer abbia accesso al processor o che integri la logica
                # Per semplicità, assumiamo che il tokenizer possa creare la rappresentazione testuale
                # da un dizionario processato. Dobbiamo processarlo prima.
                temp_processor = SVGVectorProcessor() # Istanza temporanea o passata all'init
                processed_svg = temp_processor.process(svg) # Assumendo che `svg` sia un percorso se non è codice
                inputs = self.tokenizer.tokenize(processed_svg)
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            else:
                logger.error("Input SVG non valido. Deve essere una stringa SVG, un percorso o dati pre-processati.")
                return []

        except Exception as e:
             logger.error(f"Errore nella preparazione dell'input SVG: {e}", exc_info=True)
             return []


        candidates = []
        candidates_per_strategy = max(1, num_candidates // len(strategies))

        try:
            # Genera candidati con diverse strategie
            for strategy in strategies:
                if len(candidates) >= num_candidates:
                    break

                strategy_candidates = self._generate_with_strategy(
                    inputs,
                    strategy,
                    candidates_per_strategy,
                    max_length
                )
                candidates.extend(strategy_candidates)

            # Limita al numero richiesto
            candidates = candidates[:num_candidates]

            # Rimuovi duplicati mantenendo l'ordine (importante per DPO se basato su ranking implicito)
            seen = set()
            unique_candidates = []
            for c in candidates:
                if c not in seen:
                    unique_candidates.append(c)
                    seen.add(c)
            candidates = unique_candidates


            # Se non abbiamo abbastanza candidati unici, genera altri con nucleus sampling
            attempts = 0
            max_attempts = 5 # Limita i tentativi per evitare loop infiniti
            while len(candidates) < num_candidates and attempts < max_attempts:
                logger.info(f"Tentativo {attempts+1}: Generando {num_candidates - len(candidates)} candidati aggiuntivi...")
                additional = self._generate_with_strategy(
                    inputs,
                    'nucleus', # Usa sampling più vario
                    num_candidates - len(candidates), # Genera quanti ne mancano
                    max_length
                )
                # Aggiungi solo quelli non già presenti
                for c in additional:
                    if c not in seen:
                         candidates.append(c)
                         seen.add(c)
                         if len(candidates) == num_candidates:
                             break # Raggiunto il numero desiderato
                attempts += 1


            logger.info(f"Generati {len(candidates)} candidati unici finali")
            return candidates

        except Exception as e:
            logger.error(f"Errore nella generazione dei candidati: {e}", exc_info=True)
            # Potrebbe essere utile restituire i candidati generati finora
            return candidates # Restituisce ciò che è stato generato


    def _generate_with_strategy(
        self,
        inputs: Dict[str, torch.Tensor],
        strategy: str,
        num_samples: int,
        max_length: int
    ) -> List[str]:
        """
        Genera candidati utilizzando una specifica strategia di sampling.

        Args:
            inputs: Input tokenizzato.
            strategy (str): Strategia ('beam', 'nucleus', 'temperature', 'greedy').
            num_samples (int): Numero di campioni da generare.
            max_length (int): Lunghezza massima.

        Returns:
            List[str]: Lista di descrizioni generate.
        """
        logger.debug(f"Generando {num_samples} candidati con strategia {strategy}")

        # Usa GenerationConfig per coerenza
        gen_config = GenerationConfig(
            max_length=max_length + inputs['input_ids'].shape[1], # max_length rispetto all'inizio, non solo generati
            num_return_sequences=num_samples,
            pad_token_id=self.tokenizer.pad_token_id,
            eos_token_id=self.tokenizer.eos_token_id
        )

        if strategy == 'beam':
            gen_config.num_beams = max(2, num_samples) # Usa almeno 2 beam
            gen_config.early_stopping = True
            gen_config.no_repeat_ngram_size = 2
            gen_config.do_sample = False # Beam search non campiona
        elif strategy == 'nucleus':
            gen_config.do_sample = True
            gen_config.top_p = 0.92
            gen_config.top_k = 0 # Disabilita top_k se top_p è attivo
            gen_config.temperature = 0.85 # Leggermente meno casuale
            gen_config.no_repeat_ngram_size = 2
        elif strategy == 'temperature':
            gen_config.do_sample = True
            gen_config.temperature = 1.0 # Temperatura standard
            gen_config.top_k = 50
            gen_config.top_p = 1.0 # Disabilita top_p se top_k è attivo
            gen_config.no_repeat_ngram_size = 2
        elif strategy == 'greedy':
            gen_config.do_sample = False
            gen_config.num_beams = 1 # Greedy search
        else:
            raise ValueError(f"Strategia di sampling non supportata: {strategy}")

        try:
            with torch.no_grad():
                outputs = self.model.generate(
                    input_ids=inputs['input_ids'],
                    attention_mask=inputs['attention_mask'],
                    generation_config=gen_config
                )

            # Decodifica solo la parte generata
            input_length = inputs['input_ids'].shape[1]
            generated_ids = outputs[:, input_length:]
            candidates = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)

            # Post-process (es. rimuovi spazi extra)
            candidates = [c.strip() for c in candidates]
            return candidates

        except Exception as e:
             logger.error(f"Errore durante la generazione con strategia {strategy}: {e}", exc_info=True)
             return [] # Restituisce lista vuota in caso di errore

    def generate_batch_candidates(
        self,
        svg_batch: List[Union[str, Dict]], # Può essere lista di percorsi/stringhe SVG o dati pre-processati
        num_candidates: int = 16,
        max_length: int = 100,
        strategies: Optional[List[str]] = None,
        batch_size: int = 8 # Batch size per l'inferenza del modello
    ) -> List[List[str]]:
        """
        Genera candidati per un batch di SVG.

        Args:
            svg_batch (List[Union[str, Dict]]): Lista di SVG (percorsi/stringhe) o dati pre-processati.
            num_candidates (int): Numero di candidati per SVG.
            max_length (int): Lunghezza massima.
            strategies (List[str], optional): Strategie di sampling.
            batch_size (int): Dimensione del batch per inferenza.

        Returns:
            List[List[str]]: Lista di liste di candidati per ogni SVG.
        """
        all_candidates_list = []
        input_is_processed = isinstance(svg_batch[0], dict) if svg_batch else False

        # Processa in batch più piccoli per l'inferenza
        for i in tqdm(range(0, len(svg_batch), batch_size), desc="Generazione candidati batch"):
            current_batch_svg_data = svg_batch[i : i + batch_size]
            batch_results = []

            # Itera su ciascun SVG nel batch corrente
            for svg_input_data in current_batch_svg_data:
                # Genera candidati per il singolo SVG
                single_svg_candidates = self.generate_candidates(
                    svg=svg_input_data if not input_is_processed else None, # Passa None se i dati sono già processati
                    num_candidates=num_candidates,
                    max_length=max_length,
                    strategies=strategies,
                    input_is_processed=input_is_processed,
                    processed_input=svg_input_data if input_is_processed else None
                )
                batch_results.append(single_svg_candidates)

            all_candidates_list.extend(batch_results)

        return all_candidates_list

    def rank_candidates(
        self,
        svg: Union[str, Any], # Può essere SVG str/path o immagine rasterizzata
        candidates: List[str],
        reward_model,
        svg_is_rasterized: bool = False # Flag per indicare se l'input è già un'immagine
    ) -> List[Dict[str, Any]]:
        """
        Classifica i candidati utilizzando un modello di reward.

        Args:
            svg (Union[str, Any]): SVG di input (percorso/stringa) o immagine già rasterizzata.
            candidates (List[str]): Lista di descrizioni candidate.
            reward_model: Modello di reward per la valutazione.
            svg_is_rasterized (bool): Se True, `svg` è già un'immagine PIL/Tensor.

        Returns:
            List[Dict[str, Any]]: Candidati ordinati con punteggi.
        """
        if not candidates:
            return []

        try:
            if not svg_is_rasterized:
                 # Assume che il reward model o un helper possa rasterizzare
                 # Potrebbe essere necessario passare un processore qui o integrarlo
                 # nel reward model.
                 # Placeholder: Assumiamo che il reward model gestisca la stringa SVG
                 logger.warning("Assumendo che il reward model possa gestire SVG string/path. Rasterizzazione non implementata qui.")
                 svg_input_for_reward = svg # Passa la stringa/path
            else:
                 svg_input_for_reward = svg # Passa l'immagine già pronta

            # Calcola i reward (assumendo che reward_model.forward gestisca batch)
            # Il reward model dovrebbe gestire internamente la preparazione dell'immagine/testo
            rewards = reward_model.forward([svg_input_for_reward] * len(candidates), candidates)

            # Controlla il tipo di rewards (potrebbe essere tensore)
            if isinstance(rewards, torch.Tensor):
                rewards = rewards.squeeze().cpu().tolist() # Converte in lista di float

             # Assicurati che rewards sia una lista di numeri
            if not isinstance(rewards, list) or not all(isinstance(r, (float, int)) for r in rewards):
                 logger.error(f"Il modello di reward ha restituito un output non valido: {rewards}")
                 # Restituisce candidati non ordinati con score None in caso di errore
                 return [{"text": text, "score": None} for text in candidates]


            # Crea la lista di candidati con punteggi
            ranked_candidates = [
                {"text": text, "score": score} for text, score in zip(candidates, rewards)
            ]

            # Ordina per punteggio decrescente
            # Gestisci il caso in cui score sia None
            ranked_candidates.sort(key=lambda x: x["score"] if x["score"] is not None else -float('inf'), reverse=True)


            return ranked_candidates

        except Exception as e:
            logger.error(f"Errore nel ranking dei candidati: {e}", exc_info=True)
            # Restituisci candidati non ordinati con score None in caso di errore
            return [{"text": text, "score": None} for text in candidates]


# Test del generatore
if __name__ == "__main__":
    from transformers import AutoModelForCausalLM, AutoTokenizer

    # Carica un modello di esempio
    # Usa un modello piccolo e pubblico per il test, Nemotron potrebbe richiedere accesso
    try:
        # model_name = "gpt2" # Modello piccolo per test veloce
        model_name = "gpt2-medium" # Un po' più grande per testare la generazione
        model = AutoModelForCausalLM.from_pretrained(model_name)
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        # Assicurati che il tokenizer abbia pad_token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

    except Exception as model_load_e:
         print(f"Errore caricamento modello/tokenizer di test: {model_load_e}")
         print("Assicurati di avere accesso a internet e le librerie installate.")
         exit()


    # Inizializza il generatore
    generator = CandidateGenerator(model, tokenizer)

    # SVG di esempio (come stringa) - usa un esempio più complesso
    svg_example = """
    <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
        <rect x="10" y="10" width="180" height="130" fill="lightblue" stroke="black" stroke-width="2"/>
        <circle cx="60" cy="75" r="30" fill="yellow" stroke="orange" stroke-width="3"/>
        <path d="M 150 40 L 180 75 L 150 110 Z" fill="lightgreen" stroke="darkgreen" stroke-width="1"/>
        <text x="20" y="120" font-family="Arial" font-size="12" fill="black">Example Shapes</text>
    </svg>
    """

    # Simula dati processati (molto semplificato)
    processed_example = {
         'commands': [
             {'type': 'rect', 'x': 10, 'y': 10, 'width': 180, 'height': 130},
             {'type': 'circle', 'cx': 60, 'cy': 75, 'r': 30},
             {'type': 'path_command', 'command': 'M', 'params': [150, 40]},
             {'type': 'path_command', 'command': 'L', 'params': [180, 75]},
             {'type': 'path_command', 'command': 'L', 'params': [150, 110]},
             {'type': 'path_command', 'command': 'Z', 'params': []},
             {'type': 'text', 'x': 20, 'y': 120, 'content': 'Example Shapes'}
         ],
         'metadata': {'has_color': True, 'complexity_score': 80}
     }

    print("\n--- Generazione da SVG stringa/path (richiede process/tokenize integrato) ---")
    # Nota: questo test fallirà se SVGDirectTokenizer non è configurato per gestire
    # la stringa SVG direttamente o manca SVGVectorProcessor.
    # Per ora, useremo l'input pre-processato assumendo che il tokenizer lo gestisca.
    # candidates_from_str = generator.generate_candidates(svg_example, num_candidates=5, max_length=50)
    # print("\nCandidati da stringa SVG:")
    # for i, candidate in enumerate(candidates_from_str):
    #     print(f"{i+1}. {candidate}")


    print("\n--- Generazione da dati processati ---")
    # Assumiamo che il tokenizer sia di tipo SVGDirectTokenizer
    from shared.svg_core.svg_direct_tokenizer import SVGDirectTokenizer
    try:
        # Crea un tokenizer adatto
        svg_tokenizer = SVGDirectTokenizer(base_tokenizer_name=model_name, max_length=256)
        # Aggiorna il tokenizer nel modello wrapper (se NemotronModel fosse usato)
        # Qui lo passiamo direttamente al generatore
        generator_with_svg_tokenizer = CandidateGenerator(model, svg_tokenizer)

        candidates_from_processed = generator_with_svg_tokenizer.generate_candidates(
            svg=None, # Non usato quando input_is_processed=True
            num_candidates=5,
            max_length=50,
            input_is_processed=True,
            processed_input=processed_example
        )
        print("\nCandidati da dati processati:")
        for i, candidate in enumerate(candidates_from_processed):
            print(f"{i+1}. {candidate}")

    except ImportError:
         print("\nErrore: Impossibile importare SVGDirectTokenizer. Salto test con dati processati.")
    except Exception as e:
         print(f"\nErrore durante la generazione da dati processati: {e}")


    print("\n--- Generazione Batch ---")
    svg_batch_list = [processed_example, processed_example] # Usa dati processati
    batch_candidates_list = generator_with_svg_tokenizer.generate_batch_candidates(
        svg_batch_list,
        num_candidates=3,
        max_length=40,
        batch_size=2 # Dimensione batch per inferenza
    )

    print("\nCandidati generati in batch:")
    for idx, svg_cands in enumerate(batch_candidates_list):
        print(f"--- SVG {idx+1} ---")
        for i, candidate in enumerate(svg_cands):
            print(f"  {i+1}. {candidate}")