# shared/svg_core/custom_tokenizer_utils.py

# NOTA: Assicurati che questo codice sia esattamente quello fornito da <PERSON>.
# Ho corretto il presunto typo convert_texts -> token_texts in tokenize_svg.

def token_texts():
    """Restituisce la lista di nuovi token e il dizionario di conversione."""
    convert_text = {
        '<|begin_of_svg|>': '<|begin_of_svg|>',
        '<|end_of_svg|>': '<|end_of_svg|>',
        'style=': '<|begin_of_style|>',
        'd=M': '<|begin_of_path|><|M|>',
        ' M': '<|M|>',
        ' L': '<|L|>',
        ' C': '<|C|>',
        ' A': '<|A|>',
        'fill:': '<|color|>',
        'stroke-width:': '<|stroke-width|>',
        'stroke:': '<|stroke|>',
        'opacity:': '<|opacity|>',
        '\t': '<|end_of_style|>', # Attenzione: l'uso di \t potrebbe essere fragile se la formattazione cambia
        ',': '<|SEP|>',
        'none': '<|none|>',
        'None': '<|none|>',
        '\n': '<|end_of_path|>', # Attenzione: l'uso di \n potrebbe essere fragile
        'currentColor': '<|currentColor|>',
        ';': '' # Rimuove i punti e virgola
    }
    # Crea la lista dei *nuovi* token unici da aggiungere (escludendo valori vuoti o duplicati)
    new_tokens_list = sorted(list(set(val for val in convert_text.values() if val)))
    return new_tokens_list, convert_text

## Dato un tokenizer Hugging Face, aggiunge i token utili alla tokenizzazione degli SVG
## e restituisce il tokenizer aggiornato
def build_tokenizer(tokenizer):
    """Aggiunge i nuovi token SVG al tokenizer e lo restituisce."""
    new_tokens, _ = token_texts()
    print(f"Adding {len(new_tokens)} new tokens to tokenizer: {new_tokens}")
    tokenizer.add_tokens(new_tokens=new_tokens)
    # Potrebbe essere necessario configurare qui o dopo il pad_token etc.
    # Esempio:
    # if tokenizer.pad_token is None:
    #     print("Setting pad_token to eos_token")
    #     tokenizer.pad_token = tokenizer.eos_token
    return tokenizer

# Dato un svg (stringa XML) restituisce la stringa pre-tokenizzata
# applicando le sostituzioni definite in token_texts
def tokenize_svg(svg_xml_string):
    """Applica le sostituzioni testuali per preparare l'SVG alla tokenizzazione."""
    # Estrai solo la parte interna dell'XML se è in formato XML completo
    if svg_xml_string.startswith("<?xml"):
        # Estrai solo la parte interna dell'XML (i path)
        import re

        # Funzione di sostituzione per gestire correttamente gli attributi stroke
        def replacer(match):
            if "stroke" in match.group(0):
                # Estrai lo stile e il path
                style_part = match.group(1)
                d_part = match.group(2)

                # Assicurati che gli attributi stroke siano correttamente tokenizzati
                # Questo previene problemi quando stroke è seguito da altri attributi
                style_part = re.sub(r'(stroke:[^;]+);', r'\1;', style_part)
                style_part = re.sub(r'(stroke-width:[^;]+);', r'\1;', style_part)

                return f'<path style="{style_part}" d="{d_part}"'
            return match.group(0)

        # Applica la funzione di sostituzione
        svg_xml_string = re.sub(r'<path style="([^"]+)" d="([^"]+)"', replacer, svg_xml_string)

        # Estrai i path dopo la correzione
        paths = re.findall(r'<path style="([^"]+)" d="([^"]+)"', svg_xml_string)

        # Ricostruisci il formato semplificato
        simplified_svg = ""
        for style, d in paths:
            simplified_svg += f"style={style}\td={d}\n"

        svg_xml_string = simplified_svg

    # Ottieni il dizionario delle sostituzioni
    _, convert_text_dict = token_texts()

    # Copia la stringa per non modificare l'originale inaspettatamente
    processed_svg = svg_xml_string

    # Applica le sostituzioni definite nel dizionario
    for original_text, special_token in convert_text_dict.items():
        processed_svg = processed_svg.replace(original_text, special_token)

    # Aggiungi i token di inizio e fine globali
    processed_svg = f"<|begin_of_svg|>{processed_svg}<|end_of_svg|>"

    # Rimuove doppi spazi o spazi extra che potrebbero derivare dalle sostituzioni
    import re
    processed_svg = re.sub(r'\s+', ' ', processed_svg).strip()

    return processed_svg

# Questa funzione non serve direttamente per il training ma per decodificare l'output
def de_parser(data):
    #header
    res = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<svg viewBox=\"0 0 512 512\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\">\n"

    # Queste sostituzioni sembrano specifiche e potrebbero necessitare di aggiustamenti
    # in base all'output effettivo del modello e ai token usati
    data = data.replace("<path style=\"", "style=") # Inverso di una sostituzione custom?
    data = data.replace("fill", "color") # Inverso di <|color|>? Ma 'color' non è standard SVG per fill
    data = data.replace("\" d=\"", "\t")  # Inverso di <|end_of_style|>?
    data = data.replace("Z\" />\n", "\n") # Inverso di <|end_of_path|>?

    # Probabilmente serve un de-parsing più robusto basato sul dict `token_texts` inverso

    res += data

    #footer
    res += "</svg>"
    return res