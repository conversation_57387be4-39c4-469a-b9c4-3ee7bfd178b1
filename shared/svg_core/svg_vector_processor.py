"""
SVG Vector Processing Module for SVG Captioning

This module handles the direct processing of SVG files for the captioning system:
1. Parsing SVG code directly without rasterization
2. Extracting path commands and vector information
3. Collecting structural information from SVG
"""

import os
import io
import re
import xml.etree.ElementTree as ET
import numpy as np
from typing import Dict, Tuple, List, Optional, Union, Any

class SVGVectorProcessor:
    """
    Class for processing SVG files directly as vector data for the captioning system.
    """
    
    def __init__(self, 
                 max_commands: int = 512,
                 extract_metadata: bool = True,
                 simplify_paths: bool = True):
        """
        Initialize the SVG vector processor.
        
        Args:
            max_commands: Maximum number of vector commands to extract
            extract_metadata: Whether to extract metadata from SVG
            simplify_paths: Whether to simplify complex paths
        """
        self.max_commands = max_commands
        self.extract_metadata = extract_metadata
        self.simplify_paths = simplify_paths
        
        # SVG namespace
        self.svg_ns = "{http://www.w3.org/2000/svg}"
        
        # Path command patterns
        self.path_cmd_pattern = re.compile(r'([MmLlHhVvCcSsQqTtAaZz])([^MmLlHhVvCcSsQqTtAaZz]*)')
        
        # SVG element types to process
        self.vector_elements = [
            'path', 'rect', 'circle', 'ellipse', 'line', 
            'polyline', 'polygon', 'g', 'text', 'tspan'
        ]
    
    def process(self, svg_path: str) -> Dict:
        """
        Process an SVG file and return vector data.
        
        Args:
            svg_path: Path to the SVG file
            
        Returns:
            Dictionary containing processed data:
            - 'vector_commands': List of vector commands
            - 'element_structure': Hierarchical structure of SVG elements
            - 'metadata': Dictionary of extracted metadata (if extract_metadata=True)
        """
        # Check if file exists
        if not os.path.exists(svg_path):
            raise FileNotFoundError(f"SVG file not found: {svg_path}")
        
        # Read SVG file
        with open(svg_path, 'r') as f:
            svg_content = f.read()
        
        # Parse SVG
        try:
            root = ET.fromstring(svg_content)
        except ET.ParseError as e:
            raise ValueError(f"Invalid SVG format: {e}")
        
        # Extract namespace if present
        if '}' in root.tag:
            self.svg_ns = root.tag.split('}')[0] + '}'
        else:
            self.svg_ns = ""
        
        # Extract vector commands
        vector_commands = self.extract_vector_commands(root)
        
        # Extract element structure
        element_structure = self.extract_element_structure(root)
        
        # Extract metadata if requested
        metadata = None
        if self.extract_metadata:
            metadata = self.extract_svg_metadata(root, svg_content)
        
        return {
            'vector_commands': vector_commands,
            'element_structure': element_structure,
            'metadata': metadata
        }
    
    def extract_vector_commands(self, root: ET.Element) -> List[Dict]:
        """
        Extract vector commands from SVG elements.
        
        Args:
            root: Root element of the SVG
            
        Returns:
            List of dictionaries containing vector commands
        """
        commands = []
        
        # Process all elements recursively
        for element in self._get_all_elements(root):
            element_commands = self._process_element(element)
            commands.extend(element_commands)
            
            # Limit number of commands
            if len(commands) >= self.max_commands:
                commands = commands[:self.max_commands]
                break
        
        return commands
    
    def _get_all_elements(self, root: ET.Element) -> List[ET.Element]:
        """
        Get all SVG elements in depth-first order.
        
        Args:
            root: Root element of the SVG
            
        Returns:
            List of all elements
        """
        elements = []
        
        # Add root if it's an SVG element
        if root.tag.replace(self.svg_ns, '') in self.vector_elements:
            elements.append(root)
        
        # Add all children recursively
        for child in root:
            elements.extend(self._get_all_elements(child))
        
        return elements
    
    def _process_element(self, element: ET.Element) -> List[Dict]:
        """
        Process an SVG element and extract its vector commands.
        
        Args:
            element: SVG element
            
        Returns:
            List of dictionaries containing vector commands
        """
        element_type = element.tag.replace(self.svg_ns, '')
        commands = []
        
        # Process based on element type
        if element_type == 'path':
            path_commands = self._process_path(element)
            commands.extend(path_commands)
        
        elif element_type == 'rect':
            rect_commands = self._process_rect(element)
            commands.extend(rect_commands)
        
        elif element_type == 'circle':
            circle_commands = self._process_circle(element)
            commands.extend(circle_commands)
        
        elif element_type == 'ellipse':
            ellipse_commands = self._process_ellipse(element)
            commands.extend(ellipse_commands)
        
        elif element_type == 'line':
            line_commands = self._process_line(element)
            commands.extend(line_commands)
        
        elif element_type == 'polyline' or element_type == 'polygon':
            poly_commands = self._process_poly(element, element_type)
            commands.extend(poly_commands)
        
        elif element_type == 'text' or element_type == 'tspan':
            text_commands = self._process_text(element)
            commands.extend(text_commands)
        
        # Add style attributes to all commands
        style_attrs = self._extract_style_attributes(element)
        for cmd in commands:
            cmd.update(style_attrs)
        
        return commands
    
    def _process_path(self, element: ET.Element) -> List[Dict]:
        """
        Process a path element and extract its commands.
        
        Args:
            element: Path element
            
        Returns:
            List of dictionaries containing path commands
        """
        commands = []
        
        # Get path data
        d = element.get('d', '')
        if not d:
            return commands
        
        # Parse path commands
        path_commands = self.path_cmd_pattern.findall(d)
        
        for cmd_type, cmd_params in path_commands:
            # Skip empty parameters
            if not cmd_params.strip():
                continue
            
            # Parse parameters
            params = [float(p) for p in re.findall(r'[-+]?[0-9]*\.?[0-9]+', cmd_params)]
            
            commands.append({
                'type': 'path_command',
                'command': cmd_type,
                'params': params
            })
            
            # Simplify by limiting number of commands per path
            if self.simplify_paths and len(commands) >= 50:
                commands.append({
                    'type': 'path_command',
                    'command': 'simplified',
                    'params': []
                })
                break
        
        return commands
    
    def _process_rect(self, element: ET.Element) -> List[Dict]:
        """
        Process a rectangle element and convert to path commands.
        
        Args:
            element: Rectangle element
            
        Returns:
            List of dictionaries containing path commands
        """
        x = float(element.get('x', '0'))
        y = float(element.get('y', '0'))
        width = float(element.get('width', '0'))
        height = float(element.get('height', '0'))
        rx = element.get('rx')
        ry = element.get('ry')
        
        # If rx or ry is specified, it's a rounded rectangle
        if rx is not None or ry is not None:
            rx = float(rx) if rx is not None else (float(ry) if ry is not None else 0)
            ry = float(ry) if ry is not None else (float(rx) if rx is not None else 0)
            
            return [
                {'type': 'rect', 'x': x, 'y': y, 'width': width, 'height': height, 'rx': rx, 'ry': ry}
            ]
        else:
            return [
                {'type': 'rect', 'x': x, 'y': y, 'width': width, 'height': height}
            ]
    
    def _process_circle(self, element: ET.Element) -> List[Dict]:
        """
        Process a circle element.
        
        Args:
            element: Circle element
            
        Returns:
            List of dictionaries containing circle data
        """
        cx = float(element.get('cx', '0'))
        cy = float(element.get('cy', '0'))
        r = float(element.get('r', '0'))
        
        return [
            {'type': 'circle', 'cx': cx, 'cy': cy, 'r': r}
        ]
    
    def _process_ellipse(self, element: ET.Element) -> List[Dict]:
        """
        Process an ellipse element.
        
        Args:
            element: Ellipse element
            
        Returns:
            List of dictionaries containing ellipse data
        """
        cx = float(element.get('cx', '0'))
        cy = float(element.get('cy', '0'))
        rx = float(element.get('rx', '0'))
        ry = float(element.get('ry', '0'))
        
        return [
            {'type': 'ellipse', 'cx': cx, 'cy': cy, 'rx': rx, 'ry': ry}
        ]
    
    def _process_line(self, element: ET.Element) -> List[Dict]:
        """
        Process a line element.
        
        Args:
            element: Line element
            
        Returns:
            List of dictionaries containing line data
        """
        x1 = float(element.get('x1', '0'))
        y1 = float(element.get('y1', '0'))
        x2 = float(element.get('x2', '0'))
        y2 = float(element.get('y2', '0'))
        
        return [
            {'type': 'line', 'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2}
        ]
    
    def _process_poly(self, element: ET.Element, element_type: str) -> List[Dict]:
        """
        Process a polyline or polygon element.
        
        Args:
            element: Polyline or polygon element
            element_type: 'polyline' or 'polygon'
            
        Returns:
            List of dictionaries containing polyline/polygon data
        """
        points_str = element.get('points', '')
        if not points_str:
            return []
        
        # Parse points
        point_pairs = re.findall(r'([-+]?[0-9]*\.?[0-9]+)[,\s]+([-+]?[0-9]*\.?[0-9]+)', points_str)
        points = [(float(x), float(y)) for x, y in point_pairs]
        
        return [
            {'type': element_type, 'points': points}
        ]
    
    def _process_text(self, element: ET.Element) -> List[Dict]:
        """
        Process a text element.
        
        Args:
            element: Text element
            
        Returns:
            List of dictionaries containing text data
        """
        x = float(element.get('x', '0'))
        y = float(element.get('y', '0'))
        text_content = element.text or ""
        
        return [
            {'type': 'text', 'x': x, 'y': y, 'content': text_content}
        ]
    
    def _extract_style_attributes(self, element: ET.Element) -> Dict:
        """
        Extract style attributes from an element.
        
        Args:
            element: SVG element
            
        Returns:
            Dictionary of style attributes
        """
        style_attrs = {}
        
        # Common style attributes
        attrs = ['fill', 'stroke', 'stroke-width', 'opacity', 'font-size', 'font-family']
        
        # Extract attributes
        for attr in attrs:
            if attr in element.attrib:
                style_attrs[attr] = element.get(attr)
        
        # Extract style attribute
        style = element.get('style', '')
        if style:
            style_items = [item.strip() for item in style.split(';') if item.strip()]
            for item in style_items:
                if ':' in item:
                    key, value = item.split(':', 1)
                    style_attrs[key.strip()] = value.strip()
        
        return style_attrs
    
    def extract_element_structure(self, root: ET.Element) -> Dict:
        """
        Extract hierarchical structure of SVG elements.
        
        Args:
            root: Root element of the SVG
            
        Returns:
            Dictionary representing the element structure
        """
        def process_element(element):
            element_type = element.tag.replace(self.svg_ns, '')
            
            # Skip non-SVG elements
            if element_type not in self.vector_elements and element_type != 'svg':
                return None
            
            # Create element data
            element_data = {
                'type': element_type,
                'attributes': {k: v for k, v in element.attrib.items()},
                'children': []
            }
            
            # Add text content if present
            if element.text and element.text.strip():
                element_data['text'] = element.text.strip()
            
            # Process children
            for child in element:
                child_data = process_element(child)
                if child_data:
                    element_data['children'].append(child_data)
            
            return element_data
        
        return process_element(root)
    
    def extract_svg_metadata(self, root: ET.Element, svg_content: str) -> Dict:
        """
        Extract metadata from SVG content.
        
        Args:
            root: Root element of the SVG
            svg_content: SVG content as string
            
        Returns:
            Dictionary containing metadata:
            - 'element_count': Total number of elements
            - 'element_types': Dictionary with counts of each element type
            - 'has_text': Boolean indicating if SVG contains text
            - 'has_color': Boolean indicating if SVG contains color
            - 'viewBox': SVG viewBox dimensions
            - 'complexity': Estimated complexity score
        """
        try:
            # Count elements
            elements = root.findall('.//*')
            element_count = len(elements)
            
            # Count element types
            element_types = {}
            for element in elements:
                tag = element.tag.replace(self.svg_ns, '')
                element_types[tag] = element_types.get(tag, 0) + 1
            
            # Check for text
            text_elements = root.findall(f'.//{self.svg_ns}text') + root.findall(f'.//{self.svg_ns}tspan')
            has_text = len(text_elements) > 0
            
            # Check for color
            has_color = False
            color_attrs = ['fill', 'stroke']
            for element in elements:
                for attr in color_attrs:
                    if attr in element.attrib:
                        value = element.attrib[attr]
                        if value and value.lower() not in ['none', '#000000', '#000', 'black']:
                            has_color = True
                            break
                if has_color:
                    break
            
            # Get viewBox
            viewBox = None
            if 'viewBox' in root.attrib:
                viewBox = root.attrib['viewBox']
            elif 'width' in root.attrib and 'height' in root.attrib:
                viewBox = f"0 0 {root.attrib['width']} {root.attrib['height']}"
            
            # Calculate complexity score (simple heuristic)
            complexity = element_count
            complexity += len(text_elements) * 2  # Text adds complexity
            complexity += sum([len(element.attrib) for element in elements])  # Attributes add complexity
            
            # Count path commands
            path_count = 0
            for path in root.findall(f'.//{self.svg_ns}path'):
                d = path.get('d', '')
                path_count += len(self.path_cmd_pattern.findall(d))
            complexity += path_count  # Path commands add complexity
            
            return {
                'element_count': element_count,
                'element_types': element_types,
                'has_text': has_text,
                'has_color': has_color,
                'viewBox': viewBox,
                'complexity': complexity,
                'path_command_count': path_count
            }
            
        except Exception as e:
            print(f"Error extracting metadata: {e}")
            return {
                'element_count': 0,
                'element_types': {},
                'has_text': False,
                'has_color': False,
                'viewBox': None,
                'complexity': 0,
                'error': str(e)
            }

# Example usage
if __name__ == "__main__":
    # Create processor
    processor = SVGVectorProcessor(
        max_commands=512,
        extract_metadata=True,
        simplify_paths=True
    )
    
    # Example SVG path
    svg_path = "example.svg"
    
    # Process SVG if file exists
    if os.path.exists(svg_path):
        result = processor.process(svg_path)
        print(f"Vector commands count: {len(result['vector_commands'])}")
        print(f"First few commands: {result['vector_commands'][:3]}")
        print(f"Metadata: {result['metadata']}")
    else:
        print(f"Example file {svg_path} not found. Create an SVG file to test.")
