# SVG Core

Questa directory contiene le funzionalità core per la gestione degli SVG.

## File Principali

- **custom_tokenizer_utils.py**: Implementazione del tokenizer custom per SVG
- **svg_direct_tokenizer.py**: Implementazione avanzata del tokenizer SVG
- **svg_vector_processor.py**: Processore per la conversione di SVG in rappresentazioni vettoriali
- **evaluation.py**: Funzioni per la valutazione delle didascalie generate
- **text_generation.py**: Utility per la generazione di testo
- **nemotron_model.py**: Wrapper per il modello Nemotron
- **reward_model.py**: Implementazione del modello di reward per DPO/RLHF

## Tokenizer Custom

Il tokenizer custom (`custom_tokenizer_utils.py`) implementa le seguenti funzionalità:

- `token_texts()`: Restituisce la lista di token SVG e il dizionario di conversione
- `build_tokenizer()`: Aggiunge i token SVG a un tokenizer esistente
- `tokenize_svg()`: Converte una stringa SVG in una rappresentazione tokenizzata
- `de_parser()`: Converte una rappresentazione tokenizzata in una stringa SVG

## Utilizzo

```python
from shared.svg_core.custom_tokenizer_utils import build_tokenizer, tokenize_svg
from transformers import AutoTokenizer

# Carica un tokenizer base
tokenizer = AutoTokenizer.from_pretrained("nvidia/Nemotron-Mini-4B-Instruct")

# Aggiungi i token SVG
tokenizer = build_tokenizer(tokenizer)

# Tokenizza un SVG
svg_string = "<?xml version=\"1.0\" encoding=\"utf-8\"?>..."
tokenized_svg = tokenize_svg(svg_string)
```
