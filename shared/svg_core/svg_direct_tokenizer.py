"""
SVG Direct Tokenization Module for SVG Captioning

Converts processed SVG vector data into token sequences suitable for LLMs,
using a base tokenizer extended with SVG-specific special tokens.
"""

import os
import json
import numpy as np
import torch
from transformers import AutoTokenizer
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class SVGDirectTokenizer:
    """Directly tokenizes processed SVG vector data."""

    def __init__(self,
                 base_tokenizer_name: str = "nvidia/Nemotron-Mini-4B-Instruct",
                 max_length: int = 512,
                 special_tokens_path: Optional[str] = None,
                 auth_token: Optional[str] = None,
                 precision: int = 2): # Precision for formatting numbers
        """
        Initialize the SVG tokenizer.

        Args:
            base_tokenizer_name (str): HF name or path of the base tokenizer.
            max_length (int): Maximum sequence length for padding/truncation.
            special_tokens_path (Optional[str]): Path to JSON file with additional special tokens.
            auth_token (Optional[str]): HF token (reads from env if None).
            precision (int): Decimal precision for formatting coordinates/params.
        """
        self.max_length = max_length
        self.precision = precision
        self._num_format = f"{{:.{precision}f}}" # Format string like "{:.2f}"

        # Get token: argument -> environment -> default (CLI login)
        if auth_token is None:
            auth_token = os.environ.get("HUGGING_FACE_HUB_TOKEN")
            logger.info(f"HF Token: {'Found in env' if auth_token else 'Not found in env, trying default login'}")
        hf_auth_token = auth_token if isinstance(auth_token, str) else None
        use_auth_token_flag = True if hf_auth_token is None else None

        logger.info(f"Loading base tokenizer: {base_tokenizer_name}")
        try:
            # Load base tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                base_tokenizer_name,
                token=hf_auth_token,
                use_auth_token=use_auth_token_flag
            )
        except Exception as e:
            logger.error(f"Failed to load base tokenizer {base_tokenizer_name}: {e}", exc_info=True)
            raise

        # --- Define Core SVG Special Tokens ---
        # These should ideally be consistent across the project
        self.special_tokens = {
            # Structure
            "svg_start": "<SVG_START>", "svg_end": "<SVG_END>",
            "elem_start": "<ELEM_START>", "elem_end": "<ELEM_END>", # Generic element markers? Maybe not needed if type tokens used
            "meta_start": "<META_START>", "meta_end": "<META_END>",
            # Element Types
            "path": "<PATH>", "rect": "<RECT>", "circle": "<CIRCLE>",
            "ellipse": "<ELLIPSE>", "line": "<LINE>", "polyline": "<POLYLINE>",
            "polygon": "<POLYGON>", "text": "<TEXT>", "group": "<GROUP>",
            # Path Commands
            "M": "<M>", "L": "<L>", "H": "<H>", "V": "<V>",
            "C": "<C>", "S": "<S>", "Q": "<Q>", "T": "<T>",
            "A": "<A>", "Z": "<Z>",
            # Attributes / Style
            "fill": "<FILL>", "stroke": "<STROKE>", "style": "<STYLE>",
            "width": "<WIDTH>", "height": "<HEIGHT>", "x": "<X>", "y": "<Y>", # Common attributes
            "cx": "<CX>", "cy": "<CY>", "r": "<R>", "rx": "<RX>", "ry": "<RY>",
            "x1": "<X1>", "y1": "<Y1>", "x2": "<X2>", "y2": "<Y2>",
            "points": "<POINTS>", "d": "<PATH_D>", "content": "<CONTENT>",
            # Metadata / Classification Tokens (as used in filtering)
            "style_bw": "<STYLE_BW>", "style_color": "<STYLE_COLOR>",
            "style_simple": "<STYLE_SIMPLE>", "style_complex": "<STYLE_COMPLEX>",
            # Separators / Unknown
            "sep": "<SEP>", # General purpose separator?
            "unknown": "<UNKNOWN>"
        }

        # Path command mapping (lowercase commands mapped to uppercase tokens for consistency)
        self.path_cmd_mapping = {
            k.upper(): v for k, v in self.special_tokens.items() if len(k) == 1 and k.isalpha() and k.upper() in "MLHVCSQTAZ"
        }
        for cmd_lower in "mlhvcsqtaz":
            self.path_cmd_mapping[cmd_lower] = self.path_cmd_mapping[cmd_lower.upper()]

        # Element type mapping
        self.element_type_mapping = {
            tag: token for tag, token in self.special_tokens.items()
            if tag in ["path", "rect", "circle", "ellipse", "line", "polyline", "polygon", "text", "group"]
        }
        # Map specific command types from processor if different
        self.element_type_mapping['path_command'] = self.special_tokens["path"] # Map 'path_command' type from processor to <PATH>

        # Attribute mapping
        self.attribute_mapping = {
            attr: token for attr, token in self.special_tokens.items()
            if attr in ["fill", "stroke", "style", "width", "height", "x", "y", "cx", "cy", "r", "rx", "ry", "x1", "y1", "x2", "y2", "points", "d", "content"]
        }


        # Load additional special tokens if provided
        if special_tokens_path and os.path.exists(special_tokens_path):
            try:
                with open(special_tokens_path, 'r') as f:
                    additional_tokens = json.load(f)
                    # Add them carefully, avoiding overwrites unless intended
                    for key, value in additional_tokens.items():
                         if key not in self.special_tokens:
                             self.special_tokens[key] = value
                         else:
                              logger.warning(f"Special token key '{key}' from JSON already exists. Keeping original value.")
                logger.info(f"Loaded additional special tokens from {special_tokens_path}")
            except Exception as e:
                logger.error(f"Failed to load special tokens from {special_tokens_path}: {e}")

        # Add *all* defined special tokens to the base tokenizer
        num_added = self.tokenizer.add_special_tokens(
            {"additional_special_tokens": list(self.special_tokens.values())}
        )
        if num_added > 0:
            logger.info(f"Added {num_added} special SVG tokens to the tokenizer vocabulary.")
            logger.debug(f"Final special tokens map: {self.special_tokens}")
        else:
             logger.info("No new special tokens were added (they might have existed already).")

        # Ensure pad token exists after potentially adding tokens
        self._ensure_pad_token()
        logger.info(f"Tokenizer initialized. Vocab size: {len(self.tokenizer)}, Max length: {self.max_length}")

    def _ensure_pad_token(self):
        """Ensure the tokenizer has a pad token, setting it to EOS if missing."""
        if self.tokenizer.pad_token is None:
            if self.tokenizer.eos_token is not None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
                logger.warning(f"Tokenizer missing pad_token, setting it to eos_token ('{self.tokenizer.eos_token}', ID: {self.tokenizer.pad_token_id}).")
            else:
                logger.error("CRITICAL: Tokenizer lacks both pad_token and eos_token. Cannot ensure padding.")
                # Or add a new token, but this requires resizing model embeddings again
                # self.tokenizer.add_special_tokens({'pad_token': '[PAD]'})
                # self.tokenizer.pad_token_id = self.tokenizer.convert_tokens_to_ids('[PAD]')


    def _format_number(self, num):
        """Formats a number according to the defined precision."""
        try:
            # Attempt to convert to float first, handle potential errors
            f_num = float(num)
            return self._num_format.format(f_num)
        except (ValueError, TypeError):
            # If conversion fails, return the original string representation
            logger.debug(f"Could not format '{num}' as float, returning as string.")
            return str(num)

    def _create_text_representation(self, processed_data: Dict[str, Any]) -> str:
        """
        Create a linearized text representation of SVG data using special tokens.
        """
        vector_commands = processed_data.get('commands', []) # Changed key name based on processor output
        metadata = processed_data.get('metadata', {})
        structure = processed_data.get('structure', {}) # Optional structure info

        # Determine style and complexity from metadata (used for initial tokens)
        style_token_key = "style_color" if metadata.get('has_color', False) else "style_bw"
        # Use complexity score if available, default to simple
        complexity_score = metadata.get('complexity_score', 0)
        complexity_threshold = 50 # Example threshold
        complexity_token_key = "style_complex" if complexity_score > complexity_threshold else "style_simple"

        text_parts = [
            self.special_tokens['svg_start'],
            self.special_tokens.get(style_token_key, self.special_tokens['unknown']),
            self.special_tokens.get(complexity_token_key, self.special_tokens['unknown'])
        ]

        # Add metadata section
        if metadata:
            text_parts.append(self.special_tokens['meta_start'])
            # Add key metadata fields
            if 'element_count' in metadata: text_parts.append(f"count:{metadata['element_count']}")
            if 'viewBox' in metadata: text_parts.append(f"viewBox:{metadata['viewBox']}")
            # Add element type counts
            if 'element_types' in metadata and isinstance(metadata['element_types'], dict):
                for elem_type, count in sorted(metadata['element_types'].items()):
                    text_parts.append(f"{elem_type}:{count}")
            text_parts.append(self.special_tokens['meta_end'])

        # Add vector commands sequentially
        for cmd_data in vector_commands:
            cmd_type = cmd_data.get('type', 'unknown')
            element_token = self.element_type_mapping.get(cmd_type, self.special_tokens['unknown'])
            text_parts.append(element_token)

            if cmd_type == 'path_command':
                command = cmd_data.get('command', '')
                params = cmd_data.get('params', [])
                cmd_token = self.path_cmd_mapping.get(command, f"<{command.upper()}>") # Default token if unknown
                text_parts.append(cmd_token)
                text_parts.extend([self._format_number(p) for p in params])
                # Add Z token explicitly if it was the command
                if command.upper() == 'Z':
                    pass # Token is already added
                # Consider adding explicit end-of-path if needed? Or assume next element starts new path implicitly.

            elif cmd_type in ['rect', 'circle', 'ellipse', 'line']:
                 # Add key geometric attributes using special tokens if mapped
                for key, value in sorted(cmd_data.items()):
                    if key != 'type' and key in self.attribute_mapping: # Only include mapped attributes
                        attr_token = self.attribute_mapping[key]
                        text_parts.append(attr_token)
                        text_parts.append(self._format_number(value)) # Format numbers
                    # Can add simple style info here if needed, e.g., fill color token
                    # if key == 'fill': text_parts.extend([self.special_tokens['fill'], str(value)])

            elif cmd_type in ['polyline', 'polygon']:
                points = cmd_data.get('points', [])
                if points:
                    text_parts.append(self.attribute_mapping['points'])
                    # Flatten points with formatting
                    formatted_points = [self._format_number(coord) for point in points for coord in point]
                    text_parts.extend(formatted_points)

            elif cmd_type == 'text':
                text_parts.append(self.attribute_mapping['x'])
                text_parts.append(self._format_number(cmd_data.get('x', 0)))
                text_parts.append(self.attribute_mapping['y'])
                text_parts.append(self._format_number(cmd_data.get('y', 0)))
                # Add text content carefully, maybe truncated
                content = str(cmd_data.get('content', ''))[:100] # Limit length
                text_parts.append(self.attribute_mapping['content'])
                text_parts.append(content) # Add raw content after token

            # Add style attributes common to many elements? Optional.
            # fill = cmd_data.get('fill')
            # if fill and fill != 'none': text_parts.extend([self.special_tokens['fill'], str(fill)])
            # stroke = cmd_data.get('stroke')
            # if stroke and stroke != 'none': text_parts.extend([self.special_tokens['stroke'], str(stroke)])

            # Consider adding </element> token? Might make seq too long/redundant if type token is clear.
            # text_parts.append(self.special_tokens['elem_end'])

        text_parts.append(self.special_tokens['svg_end'])

        # Join with spaces - ensure no double spaces accidentally
        return " ".join(filter(None, text_parts))


    def tokenize(self, processed_data: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """
        Tokenizes the processed SVG data dictionary.

        Args:
            processed_data (Dict[str, Any]): Output from SVGVectorProcessor.

        Returns:
            Dict[str, torch.Tensor]: {'input_ids': ..., 'attention_mask': ...}
        """
        if not isinstance(processed_data, dict):
            logger.error("Invalid input: processed_data must be a dictionary.")
            # Return empty tensors on error
            return {'input_ids': torch.tensor([[]], dtype=torch.long), 'attention_mask': torch.tensor([[]], dtype=torch.long)}

        try:
            text_representation = self._create_text_representation(processed_data)
            logger.debug(f"Generated text representation (sample): {text_representation[:200]}...")
        except Exception as e:
            logger.error(f"Error creating text representation: {e}", exc_info=True)
            return {'input_ids': torch.tensor([[]], dtype=torch.long), 'attention_mask': torch.tensor([[]], dtype=torch.long)}

        try:
            # Use the HF tokenizer
            tokenized_output = self.tokenizer(
                text_representation,
                max_length=self.max_length,
                padding="max_length", # Pad to max_length
                truncation=True,      # Truncate if longer
                return_tensors="pt",  # Return PyTorch tensors
                add_special_tokens=True # Add BOS/EOS if the base tokenizer uses them
            )
            logger.debug(f"Tokenization successful. Shape: {tokenized_output['input_ids'].shape}")
            return tokenized_output
        except Exception as e:
            logger.error(f"Error during HuggingFace tokenization: {e}", exc_info=True)
            return {'input_ids': torch.tensor([[]], dtype=torch.long), 'attention_mask': torch.tensor([[]], dtype=torch.long)}


    def decode(self, token_ids: Union[List[int], torch.Tensor]) -> str:
        """Decodes token IDs back to text, skipping special tokens."""
        if isinstance(token_ids, torch.Tensor):
            # Ensure it's on CPU and convert to list
            token_ids = token_ids.cpu().numpy().tolist()
            # Handle potential nested lists if it was a batch
            if token_ids and isinstance(token_ids[0], list):
                token_ids = token_ids[0] # Decode only the first sequence if batch

        return self.tokenizer.decode(token_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)

    def get_vocabulary_size(self) -> int:
        """Returns the current vocabulary size."""
        return len(self.tokenizer)

# Example Usage Block
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logger.info("Running SVGDirectTokenizer example...")

    # Use a small public tokenizer for testing
    test_tokenizer_name = "gpt2"
    try:
        svg_tokenizer = SVGDirectTokenizer(base_tokenizer_name=test_tokenizer_name, max_length=128)
        logger.info(f"Tokenizer '{test_tokenizer_name}' loaded and extended.")
        logger.info(f"Vocab size after adding SVG tokens: {svg_tokenizer.get_vocabulary_size()}")

        # Example processed data (simplified)
        example_svg_data = {
            'commands': [
                {'type': 'rect', 'x': 10.5, 'y': 10.5, 'width': 100.0, 'height': 50.0},
                {'type': 'circle', 'cx': 50.0, 'cy': 50.0, 'r': 25.0},
                {'type': 'path_command', 'command': 'M', 'params': [10, 10]},
                {'type': 'path_command', 'command': 'L', 'params': [100, 100]},
                {'type': 'path_command', 'command': 'Z', 'params': []}
            ],
            'metadata': {
                'element_count': 3, 'has_color': False, 'complexity_score': 10
            }
        }

        text_repr = svg_tokenizer._create_text_representation(example_svg_data)
        print("\n--- Text Representation ---")
        print(text_repr)

        tokenized = svg_tokenizer.tokenize(example_svg_data)
        print("\n--- Tokenized Output ---")
        if tokenized['input_ids'].nelement() > 0:
            print(f"Input IDs: {tokenized['input_ids']}")
            print(f"Attention Mask: {tokenized['attention_mask']}")
            # Decode back
            decoded = svg_tokenizer.decode(tokenized['input_ids'])
            print("\n--- Decoded Output (special tokens skipped) ---")
            print(decoded)
        else:
             print("Tokenization failed.")

    except Exception as main_e:
        logger.error(f"Error in SVGDirectTokenizer example: {main_e}", exc_info=True)