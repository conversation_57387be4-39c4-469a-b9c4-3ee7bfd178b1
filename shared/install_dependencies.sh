#!/bin/bash

# Script per installare dipendenze di sistema e Python per il progetto TESI_EDILUZIO

echo ">>> Aggiornamento apt e installazione dipendenze di sistema (richiede sudo)..."
sudo apt-get update && sudo apt-get install -y \
    python3-pip \
    python3-venv \
    libcairo2-dev \
    pkg-config \
    # Aggiungere altre dipendenze di sistema se necessarie (es. build-essential, git)

# Controlla se l'installazione è riuscita
if [ $? -ne 0 ]; then
    echo "Errore durante l'installazione delle dipendenze di sistema."
    exit 1
fi

echo ">>> Creazione/Attivazione ambiente virtuale Python (.venv)..."
# Assumiamo che lo script sia eseguito dalla root del progetto TESI_EDILUZIO
# o che il percorso a shared/ sia noto
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$SCRIPT_DIR/.." # Va alla directory TESI_EDILUZIO
VENV_DIR="$PROJECT_ROOT/.venv"
SHARED_DIR="$PROJECT_ROOT/shared"

if [ ! -d "$VENV_DIR" ]; then
    python3 -m venv "$VENV_DIR"
    echo "Ambiente virtuale creato in $VENV_DIR"
fi

# Attiva l'ambiente virtuale
source "$VENV_DIR/bin/activate"
echo "Ambiente virtuale attivato."

echo ">>> Installazione/Aggiornamento dipendenze Python da $SHARED_DIR/requirements.txt..."
# Usa il file requirements condiviso
pip install --upgrade pip
pip install -r "$SHARED_DIR/requirements.txt"

# Controlla se l'installazione Python è riuscita
if [ $? -ne 0 ]; then
    echo "Errore durante l'installazione delle dipendenze Python."
    # Disattiva venv prima di uscire
    deactivate
    exit 1
fi

echo ">>> Setup completato con successo!"
echo "Ricorda di attivare l'ambiente virtuale con: source $VENV_DIR/bin/activate"

# Disattiva l'ambiente virtuale alla fine dello script (opzionale)
# deactivate

exit 0