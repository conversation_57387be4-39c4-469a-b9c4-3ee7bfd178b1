"""
Modulo per la gestione dei checkpoint intermedi durante l'addestramento.

Questo modulo fornisce funzionalità per salvare, caricare e gestire checkpoint
durante l'addestramento del modello di captioning SVG, permettendo di riprendere
l'addestramento da punti specifici.
"""

import os
import json
import torch
import logging
import shutil
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime
import glob

# Configurazione del logger
logger = logging.getLogger(__name__)

class CheckpointManager:
    """
    Gestore di checkpoint per l'addestramento del modello.
    
    Questa classe fornisce metodi per:
    - Salvare checkpoint a intervalli regolari
    - Caricare checkpoint per riprendere l'addestramento
    - Gestire lo spazio su disco mantenendo solo i checkpoint più recenti o migliori
    - Tracciare le metriche associate ai checkpoint
    """
    
    def __init__(
        self,
        checkpoint_dir: str = "checkpoints",
        config_path: Optional[str] = None,
        max_checkpoints: int = 5,
        save_frequency: int = 1000,
        save_best_only: bool = False,
        metric_name: str = "loss",
        metric_mode: str = "min"
    ):
        """
        Inizializza il gestore di checkpoint.
        
        Args:
            checkpoint_dir: Directory per i checkpoint
            config_path: Percorso al file di configurazione JSON (opzionale)
            max_checkpoints: Numero massimo di checkpoint da mantenere
            save_frequency: Frequenza di salvataggio dei checkpoint (in passi)
            save_best_only: Se True, salva solo i checkpoint con le migliori metriche
            metric_name: Nome della metrica da monitorare
            metric_mode: Modalità della metrica ('min' o 'max')
        """
        # Crea la directory dei checkpoint
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # Carica la configurazione
        self.config = self._load_config(config_path)
        
        # Imposta i parametri
        self.max_checkpoints = max_checkpoints
        self.save_frequency = save_frequency
        self.save_best_only = save_best_only
        self.metric_name = metric_name
        self.metric_mode = metric_mode
        
        # Inizializza il registro dei checkpoint
        self.checkpoint_registry_file = os.path.join(self.checkpoint_dir, "checkpoint_registry.json")
        self.checkpoint_registry = self._load_checkpoint_registry()
        
        # Inizializza il miglior valore della metrica
        if self.metric_mode == "min":
            self.best_metric = float('inf')
        else:
            self.best_metric = float('-inf')
        
        logger.info(f"CheckpointManager inizializzato con output in: {self.checkpoint_dir}")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Carica la configurazione da un file JSON.
        
        Args:
            config_path: Percorso al file di configurazione
            
        Returns:
            Dizionario con la configurazione
        """
        default_config = {
            "checkpoint_config": {
                "max_checkpoints": self.max_checkpoints,
                "save_frequency": self.save_frequency,
                "save_best_only": self.save_best_only,
                "metric_name": self.metric_name,
                "metric_mode": self.metric_mode
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                logger.info(f"Configurazione caricata da: {config_path}")
                
                # Aggiorna i parametri dalla configurazione
                checkpoint_config = config.get("checkpoint_config", {})
                self.max_checkpoints = checkpoint_config.get("max_checkpoints", self.max_checkpoints)
                self.save_frequency = checkpoint_config.get("save_frequency", self.save_frequency)
                self.save_best_only = checkpoint_config.get("save_best_only", self.save_best_only)
                self.metric_name = checkpoint_config.get("metric_name", self.metric_name)
                self.metric_mode = checkpoint_config.get("metric_mode", self.metric_mode)
                
                return config
            except Exception as e:
                logger.error(f"Errore nel caricamento della configurazione: {e}")
                return default_config
        else:
            logger.warning("File di configurazione non trovato, utilizzo configurazione predefinita")
            return default_config
    
    def _load_checkpoint_registry(self) -> Dict[str, Any]:
        """
        Carica il registro dei checkpoint.
        
        Returns:
            Dizionario con il registro dei checkpoint
        """
        if os.path.exists(self.checkpoint_registry_file):
            try:
                with open(self.checkpoint_registry_file, 'r') as f:
                    registry = json.load(f)
                logger.info(f"Registro dei checkpoint caricato da: {self.checkpoint_registry_file}")
                return registry
            except Exception as e:
                logger.error(f"Errore nel caricamento del registro dei checkpoint: {e}")
                return {"checkpoints": []}
        else:
            logger.info("Creazione di un nuovo registro dei checkpoint")
            return {"checkpoints": []}
    
    def _save_checkpoint_registry(self):
        """
        Salva il registro dei checkpoint.
        """
        try:
            with open(self.checkpoint_registry_file, 'w') as f:
                json.dump(self.checkpoint_registry, f, indent=2)
            logger.info(f"Registro dei checkpoint salvato in: {self.checkpoint_registry_file}")
        except Exception as e:
            logger.error(f"Errore nel salvataggio del registro dei checkpoint: {e}")
    
    def _should_save_checkpoint(self, step: int, metrics: Dict[str, float]) -> bool:
        """
        Determina se salvare un checkpoint.
        
        Args:
            step: Passo di addestramento corrente
            metrics: Metriche correnti
            
        Returns:
            True se il checkpoint deve essere salvato, False altrimenti
        """
        # Verifica se è il momento di salvare un checkpoint
        if step % self.save_frequency != 0:
            return False
        
        # Se save_best_only è True, verifica se le metriche sono migliori
        if self.save_best_only and self.metric_name in metrics:
            current_metric = metrics[self.metric_name]
            
            if self.metric_mode == "min":
                return current_metric < self.best_metric
            else:
                return current_metric > self.best_metric
        
        # Altrimenti, salva il checkpoint
        return True
    
    def _update_best_metric(self, metrics: Dict[str, float]):
        """
        Aggiorna il miglior valore della metrica.
        
        Args:
            metrics: Metriche correnti
        """
        if self.metric_name in metrics:
            current_metric = metrics[self.metric_name]
            
            if self.metric_mode == "min" and current_metric < self.best_metric:
                self.best_metric = current_metric
                logger.info(f"Miglior valore della metrica {self.metric_name} aggiornato: {self.best_metric}")
            elif self.metric_mode == "max" and current_metric > self.best_metric:
                self.best_metric = current_metric
                logger.info(f"Miglior valore della metrica {self.metric_name} aggiornato: {self.best_metric}")
    
    def _cleanup_old_checkpoints(self):
        """
        Rimuove i checkpoint più vecchi se il numero supera max_checkpoints.
        """
        if len(self.checkpoint_registry["checkpoints"]) <= self.max_checkpoints:
            return
        
        # Ordina i checkpoint per passo (dal più vecchio al più recente)
        sorted_checkpoints = sorted(
            self.checkpoint_registry["checkpoints"],
            key=lambda x: x["step"]
        )
        
        # Rimuovi i checkpoint più vecchi
        checkpoints_to_remove = sorted_checkpoints[:len(sorted_checkpoints) - self.max_checkpoints]
        
        for checkpoint in checkpoints_to_remove:
            checkpoint_path = checkpoint["path"]
            try:
                if os.path.exists(checkpoint_path):
                    if os.path.isdir(checkpoint_path):
                        shutil.rmtree(checkpoint_path)
                    else:
                        os.remove(checkpoint_path)
                    logger.info(f"Checkpoint rimosso: {checkpoint_path}")
                
                # Rimuovi il checkpoint dal registro
                self.checkpoint_registry["checkpoints"].remove(checkpoint)
            except Exception as e:
                logger.error(f"Errore nella rimozione del checkpoint {checkpoint_path}: {e}")
        
        # Salva il registro aggiornato
        self._save_checkpoint_registry()
    
    def save_checkpoint(
        self,
        model: torch.nn.Module,
        optimizer: torch.optim.Optimizer,
        step: int,
        metrics: Optional[Dict[str, float]] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Salva un checkpoint.
        
        Args:
            model: Modello da salvare
            optimizer: Ottimizzatore da salvare
            step: Passo di addestramento corrente
            metrics: Metriche correnti (opzionale)
            additional_data: Dati aggiuntivi da salvare (opzionale)
            
        Returns:
            Percorso del checkpoint salvato o None se il checkpoint non è stato salvato
        """
        # Inizializza le metriche se non fornite
        metrics = metrics or {}
        
        # Verifica se salvare il checkpoint
        if not self._should_save_checkpoint(step, metrics):
            return None
        
        # Aggiorna il miglior valore della metrica
        self._update_best_metric(metrics)
        
        # Crea il nome del checkpoint
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_name = f"checkpoint_step{step}_{timestamp}"
        checkpoint_path = os.path.join(self.checkpoint_dir, checkpoint_name)
        
        # Crea la directory del checkpoint
        os.makedirs(checkpoint_path, exist_ok=True)
        
        try:
            # Salva il modello
            if hasattr(model, 'save_pretrained'):
                model.save_pretrained(checkpoint_path)
            else:
                model_path = os.path.join(checkpoint_path, "model.pt")
                torch.save(model.state_dict(), model_path)
            
            # Salva l'ottimizzatore
            optimizer_path = os.path.join(checkpoint_path, "optimizer.pt")
            torch.save(optimizer.state_dict(), optimizer_path)
            
            # Salva i metadati
            metadata = {
                "step": step,
                "timestamp": timestamp,
                "metrics": metrics,
                "additional_data": additional_data or {}
            }
            
            metadata_path = os.path.join(checkpoint_path, "metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Aggiorna il registro dei checkpoint
            checkpoint_info = {
                "path": checkpoint_path,
                "step": step,
                "timestamp": timestamp,
                "metrics": metrics
            }
            
            self.checkpoint_registry["checkpoints"].append(checkpoint_info)
            self._save_checkpoint_registry()
            
            logger.info(f"Checkpoint salvato in: {checkpoint_path}")
            
            # Rimuovi i checkpoint più vecchi se necessario
            self._cleanup_old_checkpoints()
            
            return checkpoint_path
        
        except Exception as e:
            logger.error(f"Errore nel salvataggio del checkpoint: {e}")
            
            # Rimuovi la directory del checkpoint se c'è stato un errore
            if os.path.exists(checkpoint_path):
                try:
                    shutil.rmtree(checkpoint_path)
                except Exception as cleanup_error:
                    logger.error(f"Errore nella pulizia del checkpoint fallito: {cleanup_error}")
            
            return None
    
    def load_checkpoint(
        self,
        model: torch.nn.Module,
        optimizer: Optional[torch.optim.Optimizer] = None,
        step: Optional[int] = None,
        checkpoint_path: Optional[str] = None,
        load_best: bool = False
    ) -> Tuple[torch.nn.Module, Optional[torch.optim.Optimizer], Dict[str, Any]]:
        """
        Carica un checkpoint.
        
        Args:
            model: Modello da caricare
            optimizer: Ottimizzatore da caricare (opzionale)
            step: Passo di addestramento specifico da caricare (opzionale)
            checkpoint_path: Percorso del checkpoint da caricare (opzionale)
            load_best: Se True, carica il checkpoint con la migliore metrica
            
        Returns:
            Tupla con il modello caricato, l'ottimizzatore caricato e i metadati
        """
        # Determina il checkpoint da caricare
        if checkpoint_path:
            # Usa il percorso specificato
            pass
        elif step is not None:
            # Cerca il checkpoint con il passo specificato
            matching_checkpoints = [
                cp for cp in self.checkpoint_registry["checkpoints"]
                if cp["step"] == step
            ]
            
            if not matching_checkpoints:
                raise ValueError(f"Nessun checkpoint trovato per il passo {step}")
            
            checkpoint_path = matching_checkpoints[0]["path"]
        elif load_best:
            # Cerca il checkpoint con la migliore metrica
            if not self.checkpoint_registry["checkpoints"]:
                raise ValueError("Nessun checkpoint disponibile")
            
            if self.metric_mode == "min":
                best_checkpoint = min(
                    self.checkpoint_registry["checkpoints"],
                    key=lambda x: x["metrics"].get(self.metric_name, float('inf'))
                )
            else:
                best_checkpoint = max(
                    self.checkpoint_registry["checkpoints"],
                    key=lambda x: x["metrics"].get(self.metric_name, float('-inf'))
                )
            
            checkpoint_path = best_checkpoint["path"]
        else:
            # Carica l'ultimo checkpoint
            if not self.checkpoint_registry["checkpoints"]:
                raise ValueError("Nessun checkpoint disponibile")
            
            latest_checkpoint = max(
                self.checkpoint_registry["checkpoints"],
                key=lambda x: x["step"]
            )
            
            checkpoint_path = latest_checkpoint["path"]
        
        logger.info(f"Caricamento del checkpoint da: {checkpoint_path}")
        
        try:
            # Carica il modello
            if hasattr(model, 'from_pretrained'):
                model = model.from_pretrained(checkpoint_path)
            else:
                model_path = os.path.join(checkpoint_path, "model.pt")
                model.load_state_dict(torch.load(model_path))
            
            # Carica l'ottimizzatore se fornito
            if optimizer is not None:
                optimizer_path = os.path.join(checkpoint_path, "optimizer.pt")
                if os.path.exists(optimizer_path):
                    optimizer.load_state_dict(torch.load(optimizer_path))
                else:
                    logger.warning(f"File dell'ottimizzatore non trovato: {optimizer_path}")
            
            # Carica i metadati
            metadata_path = os.path.join(checkpoint_path, "metadata.json")
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            logger.info(f"Checkpoint caricato con successo dal passo {metadata['step']}")
            
            return model, optimizer, metadata
        
        except Exception as e:
            logger.error(f"Errore nel caricamento del checkpoint: {e}")
            raise
    
    def get_latest_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        Ottiene le informazioni sull'ultimo checkpoint.
        
        Returns:
            Dizionario con le informazioni sull'ultimo checkpoint o None se non ci sono checkpoint
        """
        if not self.checkpoint_registry["checkpoints"]:
            return None
        
        latest_checkpoint = max(
            self.checkpoint_registry["checkpoints"],
            key=lambda x: x["step"]
        )
        
        return latest_checkpoint
    
    def get_best_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        Ottiene le informazioni sul miglior checkpoint.
        
        Returns:
            Dizionario con le informazioni sul miglior checkpoint o None se non ci sono checkpoint
        """
        if not self.checkpoint_registry["checkpoints"]:
            return None
        
        if self.metric_mode == "min":
            best_checkpoint = min(
                self.checkpoint_registry["checkpoints"],
                key=lambda x: x["metrics"].get(self.metric_name, float('inf'))
            )
        else:
            best_checkpoint = max(
                self.checkpoint_registry["checkpoints"],
                key=lambda x: x["metrics"].get(self.metric_name, float('-inf'))
            )
        
        return best_checkpoint
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """
        Elenca tutti i checkpoint disponibili.
        
        Returns:
            Lista di dizionari con le informazioni sui checkpoint
        """
        return self.checkpoint_registry["checkpoints"]
    
    def delete_checkpoint(self, checkpoint_path: str) -> bool:
        """
        Elimina un checkpoint specifico.
        
        Args:
            checkpoint_path: Percorso del checkpoint da eliminare
            
        Returns:
            True se il checkpoint è stato eliminato, False altrimenti
        """
        try:
            # Verifica se il checkpoint esiste
            if not os.path.exists(checkpoint_path):
                logger.warning(f"Checkpoint non trovato: {checkpoint_path}")
                return False
            
            # Elimina il checkpoint
            if os.path.isdir(checkpoint_path):
                shutil.rmtree(checkpoint_path)
            else:
                os.remove(checkpoint_path)
            
            # Aggiorna il registro dei checkpoint
            self.checkpoint_registry["checkpoints"] = [
                cp for cp in self.checkpoint_registry["checkpoints"]
                if cp["path"] != checkpoint_path
            ]
            
            self._save_checkpoint_registry()
            
            logger.info(f"Checkpoint eliminato: {checkpoint_path}")
            return True
        
        except Exception as e:
            logger.error(f"Errore nell'eliminazione del checkpoint: {e}")
            return False
    
    def save_checkpoint_config(self, config_path: Optional[str] = None) -> str:
        """
        Salva la configurazione del gestore di checkpoint.
        
        Args:
            config_path: Percorso del file di configurazione (opzionale)
            
        Returns:
            Percorso del file di configurazione salvato
        """
        if config_path is None:
            config_path = os.path.join(self.checkpoint_dir, "checkpoint_config.json")
        
        config = {
            "checkpoint_config": {
                "max_checkpoints": self.max_checkpoints,
                "save_frequency": self.save_frequency,
                "save_best_only": self.save_best_only,
                "metric_name": self.metric_name,
                "metric_mode": self.metric_mode
            }
        }
        
        try:
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Configurazione del gestore di checkpoint salvata in: {config_path}")
            return config_path
        
        except Exception as e:
            logger.error(f"Errore nel salvataggio della configurazione: {e}")
            raise
