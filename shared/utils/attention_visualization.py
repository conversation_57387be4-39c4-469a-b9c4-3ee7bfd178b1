"""
Modulo per la visualizzazione dei pesi di attenzione nel meccanismo di condizionamento SVG.

Questo modulo fornisce funzionalità per visualizzare, salvare e analizzare i pesi di attenzione
generati dal meccanismo di attenzione multi-testa specifico per SVG.
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import torch
from typing import Dict, List, Optional, Tuple, Union, Any
import logging
from datetime import datetime

# Configurazione del logger
logger = logging.getLogger(__name__)

class AttentionVisualizer:
    """
    Classe per visualizzare e analizzare i pesi di attenzione.
    
    Questa classe fornisce metodi per:
    - Visualizzare mappe di attenzione
    - Salvare visualizzazioni in vari formati
    - Analizzare statistiche di attenzione
    - Tracciare l'evoluzione dell'attenzione durante l'addestramento
    """
    
    def __init__(
        self,
        config_path: Optional[str] = None,
        output_dir: Optional[str] = None,
        svg_element_labels: Optional[List[str]] = None
    ):
        """
        Inizializza il visualizzatore di attenzione.
        
        Args:
            config_path: Percorso al file di configurazione JSON
            output_dir: Directory di output per le visualizzazioni
            svg_element_labels: Etichette per gli elementi SVG (opzionale)
        """
        # Carica la configurazione
        self.config = self._load_config(config_path)
        
        # Imposta la directory di output
        self.output_dir = output_dir or self.config.get("visualization_config", {}).get(
            "attention_map_dir", "attention_maps"
        )
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Imposta le etichette degli elementi SVG
        self.svg_element_labels = svg_element_labels or [
            "path", "rect", "circle", "ellipse", "line", "polyline", 
            "polygon", "text", "g", "defs", "use", "symbol", 
            "clipPath", "mask", "filter", "unknown"
        ]
        
        # Inizializza il contatore per le visualizzazioni
        self.visualization_count = 0
        
        # Inizializza il dizionario per tracciare l'evoluzione dell'attenzione
        self.attention_history = {
            "mean": [],
            "max": [],
            "entropy": [],
            "steps": []
        }
        
        logger.info(f"AttentionVisualizer inizializzato con output in: {self.output_dir}")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Carica la configurazione da un file JSON.
        
        Args:
            config_path: Percorso al file di configurazione
            
        Returns:
            Dizionario con la configurazione
        """
        default_config = {
            "visualization_config": {
                "save_attention_maps": True,
                "attention_map_dir": "attention_maps",
                "max_maps_per_batch": 5,
                "save_formats": ["png"],
                "colormap": "viridis",
                "include_element_labels": True
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                logger.info(f"Configurazione caricata da: {config_path}")
                return config
            except Exception as e:
                logger.error(f"Errore nel caricamento della configurazione: {e}")
                return default_config
        else:
            logger.warning("File di configurazione non trovato, utilizzo configurazione predefinita")
            return default_config
    
    def visualize_attention_weights(
        self,
        attention_weights: torch.Tensor,
        svg_structure_info: Optional[Dict[str, torch.Tensor]] = None,
        layer_idx: Optional[int] = None,
        head_idx: Optional[int] = None,
        step: Optional[int] = None,
        title: Optional[str] = None,
        save: bool = True
    ) -> plt.Figure:
        """
        Visualizza i pesi di attenzione come una mappa di calore.
        
        Args:
            attention_weights: Pesi di attenzione [batch_size, num_heads, seq_length, seq_length]
            svg_structure_info: Informazioni sulla struttura SVG (opzionale)
            layer_idx: Indice del layer di attenzione (opzionale)
            head_idx: Indice della testa di attenzione (opzionale)
            step: Passo di addestramento corrente (opzionale)
            title: Titolo personalizzato per la visualizzazione (opzionale)
            save: Se True, salva la visualizzazione
            
        Returns:
            Figura matplotlib
        """
        # Estrai le dimensioni
        batch_size, num_heads, seq_length, _ = attention_weights.shape
        
        # Limita il numero di mappe per batch
        max_maps = min(
            batch_size,
            self.config.get("visualization_config", {}).get("max_maps_per_batch", 5)
        )
        
        # Crea una figura per ogni esempio nel batch (limitato a max_maps)
        for batch_idx in range(max_maps):
            # Se head_idx è specificato, visualizza solo quella testa
            if head_idx is not None:
                heads_to_viz = [head_idx]
            else:
                # Altrimenti visualizza tutte le teste
                heads_to_viz = range(num_heads)
            
            for h_idx in heads_to_viz:
                # Estrai i pesi di attenzione per questa testa
                attn = attention_weights[batch_idx, h_idx].detach().cpu().numpy()
                
                # Crea la figura
                fig, ax = plt.subplots(figsize=(10, 8))
                
                # Visualizza la mappa di attenzione
                cmap = self.config.get("visualization_config", {}).get("colormap", "viridis")
                im = ax.imshow(attn, cmap=cm.get_cmap(cmap), vmin=0, vmax=1)
                
                # Aggiungi una barra dei colori
                cbar = fig.colorbar(im, ax=ax)
                cbar.set_label('Peso di attenzione')
                
                # Aggiungi etichette agli assi se le informazioni sulla struttura SVG sono disponibili
                if svg_structure_info and "element_types" in svg_structure_info and \
                   self.config.get("visualization_config", {}).get("include_element_labels", True):
                    element_types = svg_structure_info["element_types"][batch_idx].detach().cpu().numpy()
                    
                    # Crea etichette per gli elementi
                    labels = [self.svg_element_labels[t] if t < len(self.svg_element_labels) else f"Type {t}" 
                             for t in element_types]
                    
                    # Imposta le etichette sugli assi
                    # Mostra solo alcune etichette se ce ne sono troppe
                    if seq_length > 20:
                        tick_indices = np.linspace(0, seq_length-1, 10, dtype=int)
                        ax.set_xticks(tick_indices)
                        ax.set_yticks(tick_indices)
                        ax.set_xticklabels([labels[i] for i in tick_indices], rotation=45, ha="right")
                        ax.set_yticklabels([labels[i] for i in tick_indices])
                    else:
                        ax.set_xticks(range(seq_length))
                        ax.set_yticks(range(seq_length))
                        ax.set_xticklabels(labels, rotation=45, ha="right")
                        ax.set_yticklabels(labels)
                
                # Imposta il titolo
                if title:
                    ax.set_title(title)
                else:
                    layer_str = f"Layer {layer_idx}" if layer_idx is not None else "Unknown Layer"
                    head_str = f"Head {h_idx}"
                    step_str = f"Step {step}" if step is not None else ""
                    ax.set_title(f"Attention Map - {layer_str}, {head_str} {step_str}")
                
                # Aggiungi etichette agli assi
                ax.set_xlabel("Token di destinazione")
                ax.set_ylabel("Token di origine")
                
                # Regola il layout
                plt.tight_layout()
                
                # Salva la figura se richiesto
                if save:
                    self._save_visualization(fig, batch_idx, layer_idx, h_idx, step)
                
                # Incrementa il contatore
                self.visualization_count += 1
                
                # Restituisci la figura per il primo esempio e la prima testa
                if batch_idx == 0 and h_idx == heads_to_viz[0]:
                    result_fig = fig
                else:
                    plt.close(fig)
        
        return result_fig
    
    def _save_visualization(
        self,
        fig: plt.Figure,
        batch_idx: int,
        layer_idx: Optional[int],
        head_idx: int,
        step: Optional[int]
    ):
        """
        Salva la visualizzazione in vari formati.
        
        Args:
            fig: Figura matplotlib
            batch_idx: Indice del batch
            layer_idx: Indice del layer
            head_idx: Indice della testa
            step: Passo di addestramento
        """
        # Crea il nome del file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        layer_str = f"layer{layer_idx}" if layer_idx is not None else "unknown_layer"
        step_str = f"_step{step}" if step is not None else ""
        filename_base = f"attention_map_batch{batch_idx}_{layer_str}_head{head_idx}{step_str}_{timestamp}"
        
        # Salva in tutti i formati specificati
        save_formats = self.config.get("visualization_config", {}).get("save_formats", ["png"])
        for fmt in save_formats:
            filepath = os.path.join(self.output_dir, f"{filename_base}.{fmt}")
            fig.savefig(filepath, format=fmt, dpi=300, bbox_inches="tight")
            logger.info(f"Visualizzazione salvata in: {filepath}")
    
    def compute_attention_statistics(
        self,
        attention_weights: torch.Tensor
    ) -> Dict[str, np.ndarray]:
        """
        Calcola statistiche sui pesi di attenzione.
        
        Args:
            attention_weights: Pesi di attenzione [batch_size, num_heads, seq_length, seq_length]
            
        Returns:
            Dizionario con statistiche
        """
        # Converti in numpy per i calcoli
        if isinstance(attention_weights, torch.Tensor):
            attn = attention_weights.detach().cpu().numpy()
        else:
            attn = attention_weights
        
        # Calcola statistiche
        mean_attention = np.mean(attn, axis=(2, 3))  # Media per ogni testa
        max_attention = np.max(attn, axis=(2, 3))    # Massimo per ogni testa
        
        # Calcola l'entropia (misura di quanto l'attenzione è distribuita)
        # Aggiungi un epsilon per evitare log(0)
        epsilon = 1e-10
        entropy = -np.sum(attn * np.log(attn + epsilon), axis=(2, 3))
        
        return {
            "mean": mean_attention,
            "max": max_attention,
            "entropy": entropy
        }
    
    def track_attention_evolution(
        self,
        attention_weights: torch.Tensor,
        step: int
    ):
        """
        Traccia l'evoluzione dell'attenzione durante l'addestramento.
        
        Args:
            attention_weights: Pesi di attenzione [batch_size, num_heads, seq_length, seq_length]
            step: Passo di addestramento corrente
        """
        # Calcola statistiche
        stats = self.compute_attention_statistics(attention_weights)
        
        # Aggiungi al dizionario di storia
        self.attention_history["mean"].append(np.mean(stats["mean"]))
        self.attention_history["max"].append(np.mean(stats["max"]))
        self.attention_history["entropy"].append(np.mean(stats["entropy"]))
        self.attention_history["steps"].append(step)
        
        logger.info(f"Statistiche di attenzione tracciate per il passo {step}")
    
    def plot_attention_evolution(self, save: bool = True) -> plt.Figure:
        """
        Visualizza l'evoluzione dell'attenzione durante l'addestramento.
        
        Args:
            save: Se True, salva la visualizzazione
            
        Returns:
            Figura matplotlib
        """
        # Verifica che ci siano dati sufficienti
        if len(self.attention_history["steps"]) < 2:
            logger.warning("Dati insufficienti per visualizzare l'evoluzione dell'attenzione")
            return None
        
        # Crea la figura
        fig, axs = plt.subplots(3, 1, figsize=(10, 12), sharex=True)
        
        # Visualizza l'evoluzione della media
        axs[0].plot(self.attention_history["steps"], self.attention_history["mean"])
        axs[0].set_ylabel("Media attenzione")
        axs[0].set_title("Evoluzione della media di attenzione")
        axs[0].grid(True)
        
        # Visualizza l'evoluzione del massimo
        axs[1].plot(self.attention_history["steps"], self.attention_history["max"])
        axs[1].set_ylabel("Massimo attenzione")
        axs[1].set_title("Evoluzione del massimo di attenzione")
        axs[1].grid(True)
        
        # Visualizza l'evoluzione dell'entropia
        axs[2].plot(self.attention_history["steps"], self.attention_history["entropy"])
        axs[2].set_ylabel("Entropia attenzione")
        axs[2].set_xlabel("Passi di addestramento")
        axs[2].set_title("Evoluzione dell'entropia di attenzione")
        axs[2].grid(True)
        
        # Regola il layout
        plt.tight_layout()
        
        # Salva la figura se richiesto
        if save:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = os.path.join(self.output_dir, f"attention_evolution_{timestamp}.png")
            fig.savefig(filepath, dpi=300, bbox_inches="tight")
            logger.info(f"Evoluzione dell'attenzione salvata in: {filepath}")
        
        return fig
    
    def visualize_attention_heads_comparison(
        self,
        attention_weights: torch.Tensor,
        layer_idx: Optional[int] = None,
        step: Optional[int] = None,
        save: bool = True
    ) -> plt.Figure:
        """
        Visualizza un confronto tra le diverse teste di attenzione.
        
        Args:
            attention_weights: Pesi di attenzione [batch_size, num_heads, seq_length, seq_length]
            layer_idx: Indice del layer di attenzione (opzionale)
            step: Passo di addestramento corrente (opzionale)
            save: Se True, salva la visualizzazione
            
        Returns:
            Figura matplotlib
        """
        # Estrai le dimensioni
        batch_size, num_heads, seq_length, _ = attention_weights.shape
        
        # Calcola statistiche per ogni testa
        stats = self.compute_attention_statistics(attention_weights)
        
        # Crea la figura
        fig, axs = plt.subplots(1, 3, figsize=(15, 5))
        
        # Visualizza la media per ogni testa
        axs[0].bar(range(num_heads), np.mean(stats["mean"], axis=0))
        axs[0].set_xlabel("Testa di attenzione")
        axs[0].set_ylabel("Media attenzione")
        axs[0].set_title("Media di attenzione per testa")
        axs[0].set_xticks(range(num_heads))
        axs[0].grid(True, axis='y')
        
        # Visualizza il massimo per ogni testa
        axs[1].bar(range(num_heads), np.mean(stats["max"], axis=0))
        axs[1].set_xlabel("Testa di attenzione")
        axs[1].set_ylabel("Massimo attenzione")
        axs[1].set_title("Massimo di attenzione per testa")
        axs[1].set_xticks(range(num_heads))
        axs[1].grid(True, axis='y')
        
        # Visualizza l'entropia per ogni testa
        axs[2].bar(range(num_heads), np.mean(stats["entropy"], axis=0))
        axs[2].set_xlabel("Testa di attenzione")
        axs[2].set_ylabel("Entropia attenzione")
        axs[2].set_title("Entropia di attenzione per testa")
        axs[2].set_xticks(range(num_heads))
        axs[2].grid(True, axis='y')
        
        # Imposta il titolo generale
        layer_str = f"Layer {layer_idx}" if layer_idx is not None else "Unknown Layer"
        step_str = f"Step {step}" if step is not None else ""
        fig.suptitle(f"Confronto teste di attenzione - {layer_str} {step_str}", fontsize=16)
        
        # Regola il layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.85)
        
        # Salva la figura se richiesto
        if save:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            layer_str_file = f"layer{layer_idx}" if layer_idx is not None else "unknown_layer"
            step_str_file = f"_step{step}" if step is not None else ""
            filepath = os.path.join(
                self.output_dir, 
                f"attention_heads_comparison_{layer_str_file}{step_str_file}_{timestamp}.png"
            )
            fig.savefig(filepath, dpi=300, bbox_inches="tight")
            logger.info(f"Confronto teste di attenzione salvato in: {filepath}")
        
        return fig
    
    def visualize_gate_values(
        self,
        gate_values: torch.Tensor,
        step: Optional[int] = None,
        save: bool = True
    ) -> plt.Figure:
        """
        Visualizza i valori del gate di condizionamento.
        
        Args:
            gate_values: Valori del gate [batch_size, seq_length]
            step: Passo di addestramento corrente (opzionale)
            save: Se True, salva la visualizzazione
            
        Returns:
            Figura matplotlib
        """
        # Converti in numpy
        if isinstance(gate_values, torch.Tensor):
            gates = gate_values.detach().cpu().numpy()
        else:
            gates = gate_values
        
        # Calcola statistiche
        mean_gate = np.mean(gates, axis=1)  # Media per ogni esempio nel batch
        
        # Crea la figura
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Visualizza i valori medi del gate per ogni esempio nel batch
        ax.bar(range(len(mean_gate)), mean_gate)
        ax.set_xlabel("Esempio nel batch")
        ax.set_ylabel("Valore medio del gate")
        
        # Imposta il titolo
        step_str = f"Step {step}" if step is not None else ""
        ax.set_title(f"Valori medi del gate di condizionamento {step_str}")
        
        ax.grid(True, axis='y')
        
        # Regola il layout
        plt.tight_layout()
        
        # Salva la figura se richiesto
        if save:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            step_str_file = f"_step{step}" if step is not None else ""
            filepath = os.path.join(
                self.output_dir, 
                f"gate_values{step_str_file}_{timestamp}.png"
            )
            fig.savefig(filepath, dpi=300, bbox_inches="tight")
            logger.info(f"Valori del gate salvati in: {filepath}")
        
        return fig
    
    def export_attention_data(self, filepath: Optional[str] = None) -> str:
        """
        Esporta i dati di evoluzione dell'attenzione in formato JSON.
        
        Args:
            filepath: Percorso del file di output (opzionale)
            
        Returns:
            Percorso del file salvato
        """
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = os.path.join(self.output_dir, f"attention_evolution_data_{timestamp}.json")
        
        # Converti i dati in liste Python
        export_data = {
            "mean": [float(x) for x in self.attention_history["mean"]],
            "max": [float(x) for x in self.attention_history["max"]],
            "entropy": [float(x) for x in self.attention_history["entropy"]],
            "steps": [int(x) for x in self.attention_history["steps"]]
        }
        
        # Salva i dati
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        logger.info(f"Dati di evoluzione dell'attenzione esportati in: {filepath}")
        return filepath


# Funzione di utilità per estrarre i pesi di attenzione dal modello
def extract_attention_weights(
    model,
    layer_indices: Optional[List[int]] = None
) -> Dict[int, torch.Tensor]:
    """
    Estrae i pesi di attenzione dal modello.
    
    Args:
        model: Modello con layer di attenzione
        layer_indices: Indici dei layer da cui estrarre i pesi (opzionale)
        
    Returns:
        Dizionario con i pesi di attenzione per ogni layer
    """
    attention_weights = {}
    
    # Se il modello è un'istanza di SVGEncoderDecoderConditioning
    if hasattr(model, 'conditioning_layers'):
        layers = model.conditioning_layers
        if layer_indices is None:
            layer_indices = range(len(layers))
        
        for idx in layer_indices:
            if idx < len(layers):
                # Accedi ai pesi di attenzione salvati
                if hasattr(layers[idx], 'last_attention_weights'):
                    attention_weights[idx] = layers[idx].last_attention_weights
    
    # Se il modello è un'istanza di EncoderDecoderIntegration
    elif hasattr(model, 'conditioning') and hasattr(model.conditioning, 'conditioning_layers'):
        layers = model.conditioning.conditioning_layers
        if layer_indices is None:
            layer_indices = range(len(layers))
        
        for idx in layer_indices:
            if idx < len(layers):
                # Accedi ai pesi di attenzione salvati
                if hasattr(layers[idx], 'last_attention_weights'):
                    attention_weights[idx] = layers[idx].last_attention_weights
    
    return attention_weights


# Funzione per aggiungere hook per salvare i pesi di attenzione
def register_attention_hooks(model):
    """
    Registra hook per salvare i pesi di attenzione durante il forward pass.
    
    Args:
        model: Modello con layer di attenzione
        
    Returns:
        Lista di handle degli hook registrati
    """
    hooks = []
    
    def save_attention_weights(module, input, output):
        # Salva i pesi di attenzione come attributo del modulo
        if len(output) > 1 and isinstance(output[1], torch.Tensor):
            module.last_attention_weights = output[1]
    
    # Se il modello è un'istanza di SVGEncoderDecoderConditioning
    if hasattr(model, 'conditioning_layers'):
        for layer in model.conditioning_layers:
            hook = layer.register_forward_hook(save_attention_weights)
            hooks.append(hook)
    
    # Se il modello è un'istanza di EncoderDecoderIntegration
    elif hasattr(model, 'conditioning') and hasattr(model.conditioning, 'conditioning_layers'):
        for layer in model.conditioning.conditioning_layers:
            hook = layer.register_forward_hook(save_attention_weights)
            hooks.append(hook)
    
    return hooks
