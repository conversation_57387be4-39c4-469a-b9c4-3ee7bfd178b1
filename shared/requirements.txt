torch>=1.10.0
transformers>=4.30.0
datasets>=2.0.0
accelerate
bitsandbytes>=0.39.0 # Per ottimizzatori 8-bit
lxml # Per parsing XML più robusto
CairoSVG>=2.5.0 # Per rasterizzazione (necessaria per CLIP Reward)
Pillow # Dipendenza per immagini
pandas # Utile per manipolazione dati/log
numpy
tensorboardX # O tensorboard, per logging
matplotlib
seaborn
plotly # Per grafici interattivi opzionali
tqdm # Barre di progresso
nltk # Per metriche valutazione (es. BLEU)
rouge-score # Per metrica ROUGE
# Aggiungere qui eventuali altre dipendenze specifiche scoperte
# es. svgpathtools se usato internamente da svg_vector_processor
# es. scikit-image se usata per image processing