# Scripts del Progetto SVG Captioning

Questa directory contiene gli script utilizzati nel progetto SVG Captioning. Gli script sono organizzati in sottodirectory in base alla loro funzione.

## Struttura delle Directory

### `/scripts/cleanup`
Script per la pulizia dei file e dei checkpoint.

- **`cleanup_all_intermediate.sh`**: Elimina tutti i checkpoint intermedi
- **`cleanup_files.sh`**: Elimina file temporanei e non necessari
- **`cleanup_intermediate_checkpoints.sh`**: Elimina checkpoint intermedi specifici
- **`cleanup_temp_files.py`**: Script Python per la pulizia dei file temporanei
- **`cleanup_unnecessary_files.sh`**: Elimina file non necessari

### `/scripts/inference`
Script per l'inferenza con modelli addestrati.

- **`run_inference_on_complex_svg.py`**: Esegue l'inferenza su SVG complessi
- **`run_inference_on_dataset.py`**: Esegue l'inferenza su un intero dataset
- **`run_real_inference.py`**: Esegue l'inferenza con modelli reali
- **`run_simple_inference.py`**: Versione semplificata dell'inferenza
- **`run_single_model_inference.py`**: Esegue l'inferenza con un singolo modello

### `/scripts/training`
Script per il training dei modelli.

- **`train_llama_from_scratch.slurm`**: Training Llama da zero
- **`train_gemma_from_scratch.slurm`**: Training Gemma da zero
- **`train_xml_slurm_token_xml.py`**: Training con tokenizer personalizzato
- **`train_xml_slurm.py`**: Training senza tokenizer personalizzato

### `/scripts/evaluation`
Script per la valutazione dei modelli.

- **`evaluate_checkpoints.py`**: Valuta i checkpoint
- **`evaluate_with_clip_score.py`**: Valuta con CLIP Score
- **`calculate_metrics.py`**: Calcola metriche standard
- **`calculate_metrics_radar.py`**: Calcola metriche per grafici radar

### `/scripts/visualization`
Script per la creazione di report e visualizzazioni.

- **`create_comprehensive_report.py`**: Crea report completi
- **`create_embedded_svg_report.py`**: Crea report con SVG incorporati
- **`create_placeholder_only_report.py`**: Crea report con placeholder
- **`create_simple_img_report.py`**: Crea report con immagini semplici
- **`generate_radar_chart.py`**: Genera grafici radar

### `/scripts/utils`
Utility varie.

- **`setup_wandb.py`**: Configura Weights & Biands
- **`setup_hf_auth.py`**: Configura l'autenticazione Hugging Face
- **`monitor_training_progress.py`**: Monitora il progresso del training
- **`test_model_load.py`**: Testa il caricamento dei modelli

### `/scripts/slurm`
Script SLURM per l'esecuzione su cluster.

- **`run_llama31_8b_no_token_multi_gpu_simple.slurm`**: Training multi-GPU per Llama
- **`run_gemma2_9b_it_no_token_multi_gpu_simple.slurm`**: Training multi-GPU per Gemma
- **`run_clip_evaluation.slurm`**: Valutazione con CLIP
- **`run_workflow.slurm`**: Esecuzione del workflow completo

### `/scripts/data_processing`
Script per la preparazione dei dati.

- **`add_svg_to_json.py`**: Aggiunge SVG ai file JSON
- **`convert_jsonl_to_json.py`**: Converte da JSONL a JSON
- **`extract_svg_examples.py`**: Estrae esempi SVG
- **`fix_svg_files.py`**: Corregge file SVG
- **`prepare_test_data.py`**: Prepara dati di test

## Utilizzo

La maggior parte degli script può essere eseguita direttamente o tramite SLURM. Gli script SLURM sono configurati per essere eseguiti sul cluster HPC con le partizioni `boost_usr_prod` o `all_usr_prod`.

### Esempio di Esecuzione di un Job SLURM

```bash
sbatch scripts/slurm/run_llama31_8b_no_token_multi_gpu_simple.slurm
```

### Esempio di Esecuzione di uno Script Python

```bash
python scripts/inference/run_inference_on_dataset.py --model_path /path/to/model --data_file /path/to/data
```

## Note

- Gli script sono configurati per utilizzare l'ambiente Python in `/work/tesi_ediluzio/svg_captioning_env/bin/python`
- La maggior parte degli script richiede l'impostazione del token Hugging Face e della API key di Weights & Biands
- Gli script di training sono configurati per utilizzare DeepSpeed per il training multi-GPU
