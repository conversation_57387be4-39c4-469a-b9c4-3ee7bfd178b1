# Analisi delle Inferenze Zero-Shot per SVG Captioning

Questo set di script permette di analizzare i risultati delle inferenze zero-shot per il progetto di SVG captioning e generare grafici informativi che possono essere caricati su Weights & Biands.

## Contenuto

- `generate_sample_data.py`: Script per generare dati di esempio per l'analisi
- `analyze_zero_shot.py`: Script principale per l'analisi e la generazione dei grafici
- `run_zero_shot_analysis.sh`: Script shell per eseguire l'intero processo di analisi

## Requisiti

- Python 3.8+
- Librerie: pandas, matplotlib, seaborn, numpy, wandb
- Account Weights & Biands (opzionale, per il tracking)

## Utilizzo

### Esecuzione completa

Il modo più semplice per eseguire l'analisi è utilizzare lo script shell:

```bash
./scripts/run_zero_shot_analysis.sh
```

Questo script:
1. Cerca dati reali di inferenza zero-shot nella directory `results/`
2. Se non ne trova, genera dati di esempio
3. Esegue l'analisi e genera i grafici
4. Carica i risultati su Weights & Biands

### Utilizzo manuale degli script

#### Generazione di dati di esempio

Se non hai dati reali di inferenza zero-shot, puoi generare dati di esempio:

```bash
python scripts/generate_sample_data.py --num_samples 200 --output_path data/sample_zero_shot_results.jsonl
```

#### Analisi dei dati

Per analizzare i dati e generare i grafici:

```bash
python scripts/analyze_zero_shot.py --results_path data/sample_zero_shot_results.jsonl --output_dir analysis/zero_shot --use_wandb
```

## Output

Lo script di analisi genera i seguenti grafici:

1. **Distribuzione della lunghezza delle didascalie**: Mostra la distribuzione delle lunghezze delle didascalie generate da ciascun modello
2. **Distribuzione dei tipi di didascalie**: Mostra la percentuale di didascalie tecniche, visive e miste per ciascun modello
3. **Confronto tra punteggi tecnici e visivi**: Confronta i punteggi medi tecnici e visivi per ciascun modello
4. **Grafico radar di confronto dei modelli**: Confronta le performance dei modelli su diverse metriche

## Integrazione con Weights & Biands

I grafici generati vengono automaticamente caricati su Weights & Biands se l'opzione `--use_wandb` è attivata. Puoi visualizzarli nella dashboard del progetto specificato.

## Personalizzazione

Puoi personalizzare l'analisi modificando i parametri degli script:

- `--num_samples`: Numero di campioni da generare (solo per `generate_sample_data.py`)
- `--output_path`: Percorso dove salvare i dati generati o i grafici
- `--wandb_project`: Nome del progetto Weights & Biands
- `--wandb_entity`: Entity di Weights & Biands (username o team)

## Note

- Se utilizzi dati reali, assicurati che siano in formato JSONL con i campi `model`, `generated_caption` e `svg`
- Per ottenere risultati significativi, è consigliabile avere almeno 50-100 campioni per modello
