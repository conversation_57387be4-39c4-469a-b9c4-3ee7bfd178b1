# Analisi del Progetto SVG Captioner

Questo set di script permette di analizzare vari aspetti del progetto SVG Captioner, generando grafici informativi che possono essere caricati su Weights & Biands.

## Contenuto

- `analyze_zero_shot.py`: <PERSON><PERSON><PERSON> delle inferenze zero-shot
- `analyze_custom_tokenizer.py`: Analisi del tokenizer personalizzato per SVG
- `analyze_lora_parameters.py`: Analisi dei parametri addestrabili nei modelli LoRA
- `analyze_checkpoint_cleanup.py`: Analisi dell'efficienza della pulizia automatica dei checkpoint
- `run_all_analyses.sh`: Script shell per eseguire tutte le analisi

## Requisiti

- Python 3.8+
- Librerie: pandas, matplotlib, seaborn, numpy, wandb, transformers, peft
- Account Weights & Biands (opzionale, per il tracking)

## Utilizzo

### Esecuzione di tutte le analisi

Il modo più semplice per eseguire tutte le analisi è utilizzare lo script shell:

```bash
./scripts/run_all_analyses.sh
```

Questo script eseguirà tutte le analisi e caricherà i risultati su Weights & Biands.

### Utilizzo manuale degli script

#### Analisi Zero-Shot

```bash
python scripts/analyze_zero_shot.py --results_path data/sample_zero_shot_results.jsonl --output_dir analysis/zero_shot --use_wandb
```

#### Analisi del Tokenizer Personalizzato

```bash
python scripts/analyze_custom_tokenizer.py --model_path "meta-llama/Llama-3.1-8B-Instruct" --data_path data/train_set_final_xml.json --output_dir analysis/tokenizer --use_custom_tokenizer --use_wandb
```

#### Analisi dei Parametri LoRA

```bash
python scripts/analyze_lora_parameters.py --model_paths outputs/llama31_8b_lora_xml_no_token_convergence outputs/gemma2_9b_it_lora_xml_no_token_convergence --output_dir analysis/lora_parameters --use_wandb
```

#### Analisi della Pulizia Automatica dei Checkpoint

```bash
python scripts/analyze_checkpoint_cleanup.py --output_dirs outputs/llama31_8b_lora_xml_no_token_convergence outputs/gemma2_9b_it_lora_xml_no_token_convergence --analysis_dir analysis/checkpoint_cleanup --use_wandb
```

## Output

Gli script di analisi generano vari grafici e tabelle:

### Analisi Zero-Shot
- Distribuzione della lunghezza delle didascalie
- Distribuzione dei tipi di didascalie
- Confronto tra punteggi tecnici e visivi
- Grafico radar di confronto dei modelli

### Analisi del Tokenizer Personalizzato
- Distribuzione del numero di token per esempio SVG
- Uso dei token speciali SVG
- Frequenza dei token
- Frequenza dei token speciali SVG

### Analisi dei Parametri LoRA
- Parametri addestrabili per modello
- Percentuale di parametri addestrabili
- Iperparametri LoRA
- Confronto dei moduli target

### Analisi della Pulizia Automatica dei Checkpoint
- Dimensione dei checkpoint per modello
- Timeline dei checkpoint
- Riduzione dell'uso del disco

## Integrazione con Weights & Biands

I grafici generati vengono automaticamente caricati su Weights & Biands se l'opzione `--use_wandb` è attivata. Puoi visualizzarli nella dashboard del progetto specificato.

## Personalizzazione

Puoi personalizzare l'analisi modificando i parametri degli script:

- `--output_dir`: Directory dove salvare i risultati dell'analisi
- `--wandb_project`: Nome del progetto Weights & Biands
- `--wandb_entity`: Entity di Weights & Biands (username o team)
- `--max_examples`: Numero massimo di esempi da analizzare (per `analyze_custom_tokenizer.py`)
- `--model_paths`: Path ai modelli LoRA da analizzare (per `analyze_lora_parameters.py`)
- `--output_dirs`: Directory di output dei modelli da analizzare (per `analyze_checkpoint_cleanup.py`)
