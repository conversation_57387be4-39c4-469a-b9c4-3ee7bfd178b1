#!/bin/bash

# Script per monitorare i job di training
# Uso: ./scripts/monitor_jobs.sh

echo "=== MONITORING JOB STATUS ==="
echo "Data: $(date)"
echo ""

echo "=== JOB IN CODA/ESECUZIONE ==="
squeue -u $USER --format="%.10i %.12P %.20j %.8u %.2t %.10M %.6D %R"
echo ""

echo "=== ULTIMI JOB COMPLETATI ==="
sacct -u $USER --starttime=today --format=JobID,JobName,State,ExitCode,Elapsed,MaxRSS,NodeList --state=COMPLETED,FAILED,CANCELLED | tail -10
echo ""

echo "=== JOB TRAINING SPECIFICI ==="
echo "Job 2584772 (llama_te):"
squeue -j 2584772 --format="%.10i %.12P %.20j %.8u %.2t %.10M %.6D %R" 2>/dev/null || echo "  Job non in coda"

echo "Job 2584773 (gemma_te):"
squeue -j 2584773 --format="%.10i %.12P %.20j %.8u %.2t %.10M %.6D %R" 2>/dev/null || echo "  Job non in coda"
echo ""

echo "=== LOG RECENTI (se job in esecuzione) ==="
if squeue -j 2584772 -h &>/dev/null; then
    echo "--- Llama Training Log (ultime 5 righe) ---"
    tail -5 logs/llama_training_2584772.out 2>/dev/null || echo "Log non ancora disponibile"
fi

if squeue -j 2584773 -h &>/dev/null; then
    echo "--- Gemma Training Log (ultime 5 righe) ---"
    tail -5 logs/gemma_training_2584773.out 2>/dev/null || echo "Log non ancora disponibile"
fi

echo ""
echo "=== UTILIZZO GPU NODI ==="
echo "Per controllare GPU usage sui nodi attivi:"
echo "ssh <nodo> nvidia-smi"
echo ""
echo "=== COMANDI UTILI ==="
echo "Cancellare job: scancel <job_id>"
echo "Dettagli job: scontrol show job <job_id>"
echo "Log errori: cat logs/<job_name>_<job_id>.err"
echo "Log output: cat logs/<job_name>_<job_id>.out"
