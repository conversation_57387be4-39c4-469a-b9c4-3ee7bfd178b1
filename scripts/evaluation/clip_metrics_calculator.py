#!/usr/bin/env python3
"""
🎯 CALCOLO METRICHE CON CLIP SCORE - VERSIONE ESSENZIALE
========================================================
Calcola CLIP Score, BLEU, ROUGE, Exact Match e genera radar chart
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

# Import essenziali
from transformers import CLIPProcessor, CLIPModel
from rouge_score import rouge_scorer

class CLIPMetricsCalculator:
    def __init__(self):
        print("🔧 Inizializzazione CLIP Metrics Calculator...")
        
        # CLIP per image-text similarity
        print("📥 Caricamento CLIP model...")
        self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
        self.clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
        
        # ROUGE scorer
        self.rouge_scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
        
        print("✅ Calculator inizializzato!")

    def calculate_clip_score(self, text: str, image_path: str) -> float:
        """Calcola CLIP score tra testo e immagine"""
        try:
            from PIL import Image
            
            if not os.path.exists(image_path):
                print(f"⚠️ Immagine non trovata: {image_path}")
                return 0.0
                
            image = Image.open(image_path).convert('RGB')
            
            # Processa input
            inputs = self.clip_processor(text=[text], images=[image], return_tensors="pt", padding=True)
            
            # Calcola features e similarity
            with torch.no_grad():
                outputs = self.clip_model(**inputs)
                logits_per_image = outputs.logits_per_image
                clip_score = logits_per_image.item()
            
            # Normalizza score (0-100)
            return max(0, min(100, clip_score))
            
        except Exception as e:
            print(f"❌ Errore CLIP score: {e}")
            return 0.0

    def calculate_bleu_score(self, reference: str, candidate: str) -> float:
        """Calcola BLEU score"""
        try:
            ref_tokens = reference.lower().split()
            cand_tokens = candidate.lower().split()
            
            if not cand_tokens:
                return 0.0
                
            bleu = sentence_bleu([ref_tokens], cand_tokens, smoothing_function=self.smoothing)
            return bleu * 100
            
        except Exception as e:
            print(f"❌ Errore BLEU: {e}")
            return 0.0

    def calculate_rouge_score(self, reference: str, candidate: str) -> Dict[str, float]:
        """Calcola ROUGE scores"""
        try:
            scores = self.rouge_scorer.score(reference, candidate)
            return {
                'rouge1': scores['rouge1'].fmeasure * 100,
                'rouge2': scores['rouge2'].fmeasure * 100,
                'rougeL': scores['rougeL'].fmeasure * 100
            }
        except Exception as e:
            print(f"❌ Errore ROUGE: {e}")
            return {'rouge1': 0.0, 'rouge2': 0.0, 'rougeL': 0.0}

    def calculate_exact_match(self, reference: str, candidate: str) -> float:
        """Calcola Exact Match"""
        ref_clean = reference.lower().strip()
        cand_clean = candidate.lower().strip()
        return 100.0 if ref_clean == cand_clean else 0.0

    def evaluate_predictions(self, predictions_file: str, images_dir: str = None) -> Dict[str, float]:
        """Valuta predizioni su tutte le metriche"""
        print(f"📊 Valutazione: {predictions_file}")
        
        if not os.path.exists(predictions_file):
            print(f"❌ File non trovato: {predictions_file}")
            return {}
            
        # Carica predizioni
        with open(predictions_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        metrics = {
            'clip_score': [],
            'bleu_score': [],
            'rouge1': [],
            'rouge2': [],
            'rougeL': [],
            'exact_match': []
        }
        
        total = len(data)
        print(f"📈 Processando {total} campioni...")
        
        for i, item in enumerate(data):
            if i % 50 == 0:
                print(f"   Progresso: {i}/{total}")
                
            reference = item.get('reference', item.get('ground_truth', ''))
            prediction = item.get('prediction', item.get('generated', ''))
            image_file = item.get('image', item.get('image_path', ''))
            
            if not reference or not prediction:
                continue
                
            # CLIP Score
            if image_file and images_dir:
                image_path = os.path.join(images_dir, image_file)
                clip_score = self.calculate_clip_score(prediction, image_path)
                metrics['clip_score'].append(clip_score)
            
            # BLEU Score
            bleu = self.calculate_bleu_score(reference, prediction)
            metrics['bleu_score'].append(bleu)
            
            # ROUGE Scores
            rouge = self.calculate_rouge_score(reference, prediction)
            metrics['rouge1'].append(rouge['rouge1'])
            metrics['rouge2'].append(rouge['rouge2'])
            metrics['rougeL'].append(rouge['rougeL'])
            
            # Exact Match
            em = self.calculate_exact_match(reference, prediction)
            metrics['exact_match'].append(em)
        
        # Calcola medie
        results = {}
        for metric, values in metrics.items():
            if values:
                results[metric] = np.mean(values)
                print(f"   {metric}: {results[metric]:.2f}")
            else:
                results[metric] = 0.0
        
        return results

def create_radar_chart(metrics_data: Dict[str, Dict[str, float]], output_path: str):
    """Crea grafico radar professionale"""
    print("📊 Creazione radar chart...")
    
    # Metriche da visualizzare
    metric_names = ['CLIP Score', 'BLEU', 'ROUGE-1', 'ROUGE-L', 'Exact Match']
    metric_keys = ['clip_score', 'bleu_score', 'rouge1', 'rougeL', 'exact_match']
    
    # Setup figura
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli
    angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=False).tolist()
    angles += angles[:1]
    
    # Colori professionali
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    
    # Plotta ogni modello
    for i, (model_name, metrics) in enumerate(metrics_data.items()):
        values = [metrics.get(key, 0) for key in metric_keys]
        values += values[:1]
        
        ax.plot(angles, values, 'o-', linewidth=3, label=model_name, 
                color=colors[i % len(colors)], markersize=8)
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    # Personalizza
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=14, fontweight='bold')
    ax.set_ylim(0, 100)
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20', '40', '60', '80', '100'], fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # Titolo e legenda
    plt.title('📊 Confronto Metriche Modelli SVG\n(CLIP Score, BLEU, ROUGE, Exact Match)', 
              fontsize=18, fontweight='bold', pad=30)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=14)
    
    # Salva
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ Radar chart salvato: {output_path}")

def main():
    print("🚀 CALCOLO METRICHE CON CLIP SCORE")
    print("=" * 50)
    
    # Inizializza
    calculator = CLIPMetricsCalculator()
    
    # Directory
    base_dir = Path("/work/tesi_ediluzio")
    
    # File di test (aggiorna con i tuoi path reali)
    test_predictions = {
        "Baseline Gemma": base_dir / "evaluation" / "baseline_gemma_predictions.json",
        "Baseline Llama": base_dir / "evaluation" / "baseline_llama_predictions.json",
        "Fine-tuned Gemma": base_dir / "evaluation" / "gemma_finetuned_predictions.json",
        "Fine-tuned Llama": base_dir / "evaluation" / "llama_finetuned_predictions.json"
    }
    
    images_dir = str(base_dir / "data" / "processed" / "images")
    
    # Calcola metriche
    all_metrics = {}
    
    for model_name, pred_file in test_predictions.items():
        if os.path.exists(pred_file):
            print(f"\n🎯 Valutazione {model_name}...")
            metrics = calculator.evaluate_predictions(str(pred_file), images_dir)
            if metrics:
                all_metrics[model_name] = metrics
        else:
            print(f"⚠️ File non trovato: {pred_file}")
    
    # Salva risultati
    output_dir = base_dir / "evaluation" / "reports"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if all_metrics:
        # Salva JSON
        metrics_file = output_dir / "clip_metrics_results.json"
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(all_metrics, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Metriche salvate: {metrics_file}")
        
        # Crea radar chart
        radar_path = output_dir / "clip_metrics_radar_chart.png"
        create_radar_chart(all_metrics, str(radar_path))
        
        print(f"\n🎉 CALCOLO COMPLETATO!")
        print(f"📁 Risultati in: {output_dir}")
    else:
        print("❌ Nessuna metrica calcolata!")

if __name__ == "__main__":
    main()
