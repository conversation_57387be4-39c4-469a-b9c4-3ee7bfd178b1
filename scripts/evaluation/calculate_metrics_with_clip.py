#!/usr/bin/env python3
"""
🎯 CALCOLO METRICHE COMPLETE CON CLIP SCORE
==========================================
Calcola tutte le metriche per baseline e modelli fine-tuned:
- CLIP Score (text-image similarity)
- BLEU Score
- ROUGE Score  
- Exact Match
- Semantic Similarity
- Genera grafico radar realistico
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Import per metriche
from transformers import CLIPProcessor, CLIPModel
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from rouge_score import rouge_scorer
import nltk

# Download NLTK data se necessario
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

class MetricsCalculator:
    def __init__(self):
        print("🔧 Inizializzazione MetricsCalculator...")
        
        # CLIP per image-text similarity
        print("📥 Caricamento CLIP model...")
        self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
        self.clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
        
        # Sentence transformer per semantic similarity
        print("📥 Caricamento SentenceTransformer...")
        self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # ROUGE scorer
        self.rouge_scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
        
        # BLEU smoothing
        self.smoothing = SmoothingFunction().method1
        
        print("✅ MetricsCalculator inizializzato!")

    def calculate_clip_score(self, text: str, image_path: str) -> float:
        """Calcola CLIP score tra testo e immagine"""
        try:
            from PIL import Image
            
            # Carica immagine
            if not os.path.exists(image_path):
                print(f"⚠️ Immagine non trovata: {image_path}")
                return 0.0
                
            image = Image.open(image_path).convert('RGB')
            
            # Processa input
            inputs = self.clip_processor(text=[text], images=[image], return_tensors="pt", padding=True)
            
            # Calcola features
            with torch.no_grad():
                outputs = self.clip_model(**inputs)
                logits_per_image = outputs.logits_per_image
                clip_score = logits_per_image.item()
            
            # Normalizza score (0-100)
            return max(0, min(100, clip_score))
            
        except Exception as e:
            print(f"❌ Errore CLIP score: {e}")
            return 0.0

    def calculate_bleu_score(self, reference: str, candidate: str) -> float:
        """Calcola BLEU score"""
        try:
            ref_tokens = reference.lower().split()
            cand_tokens = candidate.lower().split()
            
            if not cand_tokens:
                return 0.0
                
            bleu = sentence_bleu([ref_tokens], cand_tokens, smoothing_function=self.smoothing)
            return bleu * 100  # Converti in percentuale
            
        except Exception as e:
            print(f"❌ Errore BLEU: {e}")
            return 0.0

    def calculate_rouge_score(self, reference: str, candidate: str) -> Dict[str, float]:
        """Calcola ROUGE scores"""
        try:
            scores = self.rouge_scorer.score(reference, candidate)
            return {
                'rouge1': scores['rouge1'].fmeasure * 100,
                'rouge2': scores['rouge2'].fmeasure * 100,
                'rougeL': scores['rougeL'].fmeasure * 100
            }
        except Exception as e:
            print(f"❌ Errore ROUGE: {e}")
            return {'rouge1': 0.0, 'rouge2': 0.0, 'rougeL': 0.0}

    def calculate_exact_match(self, reference: str, candidate: str) -> float:
        """Calcola Exact Match"""
        ref_clean = reference.lower().strip()
        cand_clean = candidate.lower().strip()
        return 100.0 if ref_clean == cand_clean else 0.0

    def calculate_semantic_similarity(self, reference: str, candidate: str) -> float:
        """Calcola similarità semantica con sentence transformers"""
        try:
            ref_embedding = self.sentence_model.encode([reference])
            cand_embedding = self.sentence_model.encode([candidate])
            
            similarity = cosine_similarity(ref_embedding, cand_embedding)[0][0]
            return max(0, similarity * 100)  # Converti in percentuale
            
        except Exception as e:
            print(f"❌ Errore semantic similarity: {e}")
            return 0.0

    def evaluate_model(self, predictions_file: str, images_dir: str) -> Dict[str, float]:
        """Valuta un modello su tutte le metriche"""
        print(f"📊 Valutazione modello: {predictions_file}")
        
        if not os.path.exists(predictions_file):
            print(f"❌ File predizioni non trovato: {predictions_file}")
            return {}
            
        # Carica predizioni
        with open(predictions_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        metrics = {
            'clip_score': [],
            'bleu_score': [],
            'rouge1': [],
            'rouge2': [],
            'rougeL': [],
            'exact_match': [],
            'semantic_similarity': []
        }
        
        total_samples = len(data)
        print(f"📈 Processando {total_samples} campioni...")
        
        for i, item in enumerate(data):
            if i % 100 == 0:
                print(f"   Progresso: {i}/{total_samples}")
                
            reference = item.get('reference', '')
            prediction = item.get('prediction', '')
            image_file = item.get('image', '')
            
            if not reference or not prediction:
                continue
                
            # CLIP Score (se abbiamo immagine)
            if image_file and images_dir:
                image_path = os.path.join(images_dir, image_file)
                clip_score = self.calculate_clip_score(prediction, image_path)
                metrics['clip_score'].append(clip_score)
            
            # BLEU Score
            bleu = self.calculate_bleu_score(reference, prediction)
            metrics['bleu_score'].append(bleu)
            
            # ROUGE Scores
            rouge = self.calculate_rouge_score(reference, prediction)
            metrics['rouge1'].append(rouge['rouge1'])
            metrics['rouge2'].append(rouge['rouge2'])
            metrics['rougeL'].append(rouge['rougeL'])
            
            # Exact Match
            em = self.calculate_exact_match(reference, prediction)
            metrics['exact_match'].append(em)
            
            # Semantic Similarity
            sem_sim = self.calculate_semantic_similarity(reference, prediction)
            metrics['semantic_similarity'].append(sem_sim)
        
        # Calcola medie
        results = {}
        for metric, values in metrics.items():
            if values:
                results[metric] = np.mean(values)
            else:
                results[metric] = 0.0
        
        print(f"✅ Valutazione completata!")
        return results

def create_radar_chart(metrics_data: Dict[str, Dict[str, float]], output_path: str):
    """Crea grafico radar con tutte le metriche"""
    print("📊 Creazione grafico radar...")
    
    # Metriche da visualizzare
    metric_names = ['CLIP Score', 'BLEU', 'ROUGE-1', 'ROUGE-L', 'Exact Match', 'Semantic Sim.']
    metric_keys = ['clip_score', 'bleu_score', 'rouge1', 'rougeL', 'exact_match', 'semantic_similarity']
    
    # Setup figura
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il cerchio
    
    # Colori per ogni modello
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    
    # Plotta ogni modello
    for i, (model_name, metrics) in enumerate(metrics_data.items()):
        values = [metrics.get(key, 0) for key in metric_keys]
        values += values[:1]  # Chiudi il cerchio
        
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    # Personalizza grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=12)
    ax.set_ylim(0, 100)
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20', '40', '60', '80', '100'], fontsize=10)
    ax.grid(True)
    
    # Titolo e legenda
    plt.title('📊 Confronto Metriche Modelli SVG\n(CLIP Score, BLEU, ROUGE, Exact Match, Semantic Similarity)', 
              fontsize=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12)
    
    # Salva
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Grafico salvato: {output_path}")

def main():
    print("🚀 CALCOLO METRICHE CON CLIP SCORE")
    print("=" * 50)
    
    # Inizializza calculator
    calculator = MetricsCalculator()
    
    # Directory di lavoro
    base_dir = Path("/work/tesi_ediluzio")
    
    # Modelli da valutare (esempi - aggiorna con i tuoi path)
    models_to_evaluate = {
        "Baseline Gemma": {
            "predictions": base_dir / "evaluation" / "baseline_gemma_predictions.json",
            "images_dir": base_dir / "data" / "processed" / "images"
        },
        "Baseline Llama": {
            "predictions": base_dir / "evaluation" / "baseline_llama_predictions.json", 
            "images_dir": base_dir / "data" / "processed" / "images"
        },
        "Fine-tuned Gemma T9": {
            "predictions": base_dir / "evaluation" / "gemma_t9_predictions.json",
            "images_dir": base_dir / "data" / "processed" / "images"
        },
        "Fine-tuned Llama T8": {
            "predictions": base_dir / "evaluation" / "llama_t8_predictions.json",
            "images_dir": base_dir / "data" / "processed" / "images"
        }
    }
    
    # Calcola metriche per ogni modello
    all_metrics = {}
    
    for model_name, config in models_to_evaluate.items():
        print(f"\n🎯 Valutazione {model_name}...")
        
        predictions_file = str(config["predictions"])
        images_dir = str(config["images_dir"])
        
        metrics = calculator.evaluate_model(predictions_file, images_dir)
        
        if metrics:
            all_metrics[model_name] = metrics
            print(f"📊 Risultati {model_name}:")
            for metric, value in metrics.items():
                print(f"   {metric}: {value:.2f}")
        else:
            print(f"⚠️ Nessuna metrica calcolata per {model_name}")
    
    # Salva risultati
    output_dir = base_dir / "evaluation" / "reports"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Salva metriche JSON
    metrics_file = output_dir / "metrics_with_clip_score.json"
    with open(metrics_file, 'w', encoding='utf-8') as f:
        json.dump(all_metrics, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Metriche salvate: {metrics_file}")
    
    # Crea grafico radar
    if all_metrics:
        radar_chart_path = output_dir / "radar_chart_with_clip.png"
        create_radar_chart(all_metrics, str(radar_chart_path))
    
    print("\n🎉 CALCOLO METRICHE COMPLETATO!")
    print(f"📁 Risultati in: {output_dir}")

if __name__ == "__main__":
    main()
