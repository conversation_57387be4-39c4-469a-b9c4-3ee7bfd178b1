#!/bin/bash
#SBATCH --job-name=gemma_t9_resume_auto
#SBATCH --output=logs/gemma_t9_resume_7000_%j.out
#SBATCH --error=logs/gemma_t9_resume_7000_%j.err
#SBATCH --time=24:00:00
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal

echo "💎 GEMMA T9 RESUME AUTOMATICO DA CHECKPOINT-14000"
echo "=============================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $(hostname)"
echo "Timestamp: $(date)"
echo ""

# Environment setup
cd /work/tesi_ediluzio

echo "🔧 VERIFICA AMBIENTE"
echo "===================="
# Attiva ambiente FUNZIONANTE (stesso di Llama T9)
source /homes/ediluzio/.bashrc
conda activate svg_env

/homes/ediluzio/.conda/envs/svg_env/bin/python -c "
try:
    import peft
    print('✅ PEFT OK in svg_env')
except:
    print('❌ PEFT MANCANTE in svg_env')

try:
    import transformers
    print(f'✅ TRANSFORMERS {transformers.__version__} OK in svg_env')
except:
    print('❌ TRANSFORMERS MANCANTE in svg_env')
"

echo ""
echo "🎯 GPU DISPONIBILI"
echo "=================="
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv

echo ""
echo "📋 PARAMETRI RESUME TRAINING"
echo "============================"
echo "Modello: google/gemma-2-9b-it"
echo "Dataset: train_set_100k_final_90000.json"
echo "Resume da: checkpoint-14000"
echo "Output: gemma_t9_no_accumulation"
echo "Gradient accumulation: 1"
echo "Batch size per device: 2"
echo "Effective batch size: 2"
echo ""

# Verifica checkpoint
CHECKPOINT_DIR="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/checkpoint-14000"
if [ ! -d "$CHECKPOINT_DIR" ]; then
    echo "❌ ERRORE: Checkpoint non trovato: $CHECKPOINT_DIR"
    exit 1
fi

echo "✅ Checkpoint trovato: $CHECKPOINT_DIR"
echo "📊 Contenuto checkpoint:"
ls -la "$CHECKPOINT_DIR"

echo ""
echo "🚀 RESUME AUTOMATICO GEMMA T9 DA CHECKPOINT-14000"
echo "==============================================="

# Imposta token HuggingFace e cache su /work (più spazio)
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
mkdir -p /work/tesi_ediluzio/.cache/huggingface

# Resume training con SCRIPT PATCHATO per bug active_adapters
# NOTA: Lo script fa resume AUTOMATICO se trova checkpoint in output_dir
/homes/ediluzio/.conda/envs/svg_env/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_resume_fixed.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json \
    --output_dir experiments/xml_direct_input/outputs/gemma_t9_no_accumulation

TRAINING_EXIT_CODE=$?

echo ""
echo "📊 RISULTATI RESUME TRAINING"
echo "============================"

if [ $TRAINING_EXIT_CODE -eq 0 ]; then
    echo "✅ Resume training completato con successo!"
    echo "📁 Output directory: experiments/xml_direct_input/outputs/gemma_t9_no_accumulation"
    
    # Mostra ultimi checkpoint
    echo ""
    echo "📋 Checkpoint finali:"
    ls -la experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/ | tail -5
    
    # Mostra statistiche finali
    echo ""
    echo "📈 STATISTICHE FINALI:"
    echo "Checkpoint iniziale: 14000"
    echo "Training completato: $(date)"
    
else
    echo "❌ Resume training fallito con exit code: $TRAINING_EXIT_CODE"
    echo "Controlla log errori per dettagli"
fi

echo ""
echo "🎯 RESUME TRAINING COMPLETATO!"
echo "Timestamp: $(date)"
