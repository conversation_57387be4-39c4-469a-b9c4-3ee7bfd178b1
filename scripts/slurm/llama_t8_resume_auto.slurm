#!/bin/bash
#SBATCH --job-name=llama_t8_resume_auto
#SBATCH --output=logs/llama_t8_resume_auto_%j.out
#SBATCH --error=logs/llama_t8_resume_auto_%j.err
#SBATCH --time=24:00:00
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal

echo "🦙 LLAMA T8 RESUME AUTOMATICO DALL'ULTIMO CHECKPOINT"
echo "=================================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $(hostname)"
echo "Timestamp: $(date)"
echo ""

# Environment setup
cd /work/tesi_ediluzio

echo "🔧 VERIFICA AMBIENTE"
echo "===================="
# Attiva ambiente FUNZIONANTE (stesso di Llama T9)
source /homes/ediluzio/.bashrc
conda activate svg_env

/homes/ediluzio/.conda/envs/svg_env/bin/python -c "
try:
    import peft
    print('✅ PEFT OK in svg_env')
except:
    print('❌ PEFT MANCANTE in svg_env')

try:
    import transformers
    print(f'✅ TRANSFORMERS {transformers.__version__} OK in svg_env')
except:
    print('❌ TRANSFORMERS MANCANTE in svg_env')
"

echo ""
echo "🎯 GPU DISPONIBILI"
echo "=================="
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv

echo ""
echo "📋 PARAMETRI RESUME TRAINING"
echo "============================"
echo "Modello: meta-llama/Llama-3.1-8B-Instruct"
echo "Dataset: train_set_100k_final_90000.json"
echo "Output: llama_t8_24h"
echo "Gradient accumulation: 8"
echo "Batch size per device: 2"
echo "Effective batch size: 16"
echo ""

# Trova ultimo checkpoint
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"
if [ ! -d "$OUTPUT_DIR" ]; then
    echo "❌ ERRORE: Output directory non trovata: $OUTPUT_DIR"
    exit 1
fi

echo "🔍 RICERCA ULTIMO CHECKPOINT"
echo "============================"
echo "Directory: $OUTPUT_DIR"

# Lista tutti i checkpoint
CHECKPOINTS=$(ls -1 "$OUTPUT_DIR" | grep "checkpoint-" | sort -V)
if [ -z "$CHECKPOINTS" ]; then
    echo "❌ ERRORE: Nessun checkpoint trovato in $OUTPUT_DIR"
    exit 1
fi

echo "📊 Checkpoint trovati:"
for cp in $CHECKPOINTS; do
    echo "  - $cp"
done

# Trova l'ultimo checkpoint
LATEST_CHECKPOINT=$(echo "$CHECKPOINTS" | tail -1)
CHECKPOINT_DIR="$OUTPUT_DIR/$LATEST_CHECKPOINT"

echo ""
echo "✅ Ultimo checkpoint: $LATEST_CHECKPOINT"
echo "📁 Path completo: $CHECKPOINT_DIR"
echo "📊 Contenuto checkpoint:"
ls -la "$CHECKPOINT_DIR"

echo ""
echo "🚀 RESUME AUTOMATICO LLAMA T8 DALL'ULTIMO CHECKPOINT"
echo "=================================================="

# Imposta token HuggingFace
export HUGGINGFACE_HUB_TOKEN=*************************************

# Resume training con SCRIPT PATCHATO per bug active_adapters
# NOTA: Lo script fa resume AUTOMATICO se trova checkpoint in output_dir
/homes/ediluzio/.conda/envs/svg_env/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29504 \
    scripts/training/train_lora_resume_fixed.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t8_24h

TRAINING_EXIT_CODE=$?

echo ""
echo "📊 RISULTATI RESUME TRAINING"
echo "============================"

if [ $TRAINING_EXIT_CODE -eq 0 ]; then
    echo "✅ Resume training completato con successo!"
    echo "📁 Output directory: experiments/xml_direct_input/outputs/llama_t8_24h"
    
    # Mostra ultimi checkpoint
    echo ""
    echo "📋 Checkpoint finali:"
    ls -la experiments/xml_direct_input/outputs/llama_t8_24h/ | tail -5
    
    # Mostra statistiche finali
    echo ""
    echo "📈 STATISTICHE FINALI:"
    echo "Checkpoint iniziale: $LATEST_CHECKPOINT"
    echo "Training completato: $(date)"
    
else
    echo "❌ Resume training fallito con exit code: $TRAINING_EXIT_CODE"
    echo "Controlla log errori per dettagli"
fi

echo ""
echo "🎯 RESUME TRAINING COMPLETATO!"
echo "Timestamp: $(date)"
