# Path: tesi_ediluzio/data_preparation/create_filtered_xml_dataset.py
# MODIFICATO per:
# 1. Filtrare per lunghezza massima della stringa XML.
# 2. Filtrare (euristicamente) per SVG che sembrano essere solo bianco/nero/none.
# 3. <PERSON><PERSON><PERSON> il vecchio filtro basato sul conteggio di "d=".
# 4. Mantenuto 'source' e logica di base.
# NOTA: Per ottenere ESATTAMENTE 2000 campioni DIVERSIFICATI, eseguire PRIMA questo script
#       per filtrare tutti i dati (SENZA --max_samples), e POI usare split_dataset.py
#       sul file di output generato qui.

import json
import argparse
import os
import glob
import logging
import re
from tqdm import tqdm

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- NUOVA FUNZIONE PER CONTROLLO BIANCO/NERO (EURISTICO) ---
def is_likely_black_and_white(svg_data_string: str) -> bool:
    """
    Controlla euristicamente se la stringa SVG contiene solo colori
    bianco, nero, none, o variazioni comuni (#fff, #000, ecc.).
    ATTENZIONE: È una euristica, non un parser SVG completo.
    """
    if not isinstance(svg_data_string, str):
        return False # Non processabile

    # Definisci i valori permessi (case-insensitive)
    allowed_values = {
        'black', 'white', 'none',
        '#000', '#000000',
        '#fff', '#ffffff',
        'rgb(0,0,0)', 'rgb(0, 0, 0)',
        'rgb(255,255,255)', 'rgb(255, 255, 255)'
        # Aggiungi 'transparent'? A seconda di come vuoi trattarlo.
        # 'transparent'
    }

    # Pattern Regex per trovare attributi fill/stroke e stili inline
    # Cerca fill="..." o stroke="..."
    attr_pattern = re.compile(r'(?:fill|stroke)\s*=\s*["\']([^"\']+)["\']', re.IGNORECASE)
    # Cerca style="..." e poi fill/stroke all'interno
    style_pattern = re.compile(r'style\s*=\s*["\']([^"\']+)["\']', re.IGNORECASE)
    style_color_pattern = re.compile(r'(?:fill|stroke)\s*:\s*([^;"]+)', re.IGNORECASE)

    # 1. Controlla attributi fill/stroke diretti
    matches = attr_pattern.findall(svg_data_string)
    for value in matches:
        if value.strip().lower() not in allowed_values:
            # logger.debug(f"Colore non B/N trovato in attributo diretto: {value}")
            return False # Trovato colore non permesso

    # 2. Controlla stili inline
    style_matches = style_pattern.findall(svg_data_string)
    for style_content in style_matches:
        color_values_in_style = style_color_pattern.findall(style_content)
        for value in color_values_in_style:
            if value.strip().lower() not in allowed_values:
                # logger.debug(f"Colore non B/N trovato in stile inline: {value}")
                return False # Trovato colore non permesso

    # Se nessun colore non permesso è stato trovato, assumiamo sia B/N o senza colori specificati
    return True
# --- FINE FUNZIONE CONTROLLO B/N ---


def filter_dataset(input_pattern, output_file, max_length=512, require_bw=False, max_samples=None):
    """
    Filtra i dati SVG da file JSON chunk basandosi sulla lunghezza della stringa XML
    e (opzionalmente) su un controllo euristico per bianco/nero. Include "source".

    Args:
        input_pattern (str): Pattern per trovare i file JSON di input.
        output_file (str): Percorso del file JSON di output.
        max_length (int): Lunghezza massima consentita per il campo 'xml'.
        require_bw (bool): Se True, applica il filtro euristico per bianco/nero.
        max_samples (int, optional): Numero massimo di campioni da includere nell'output.
                                     Se usato, la diversità per source non è garantita.
                                     Per ottenere 2000 campioni diversi, NON usare questo
                                     parametro e usa split_dataset.py dopo.
    """
    input_files = glob.glob(input_pattern)
    if not input_files:
        logger.error(f"Nessun file trovato con il pattern: {input_pattern}")
        return

    logger.info(f"Trovati {len(input_files)} file di input. Inizio processamento...")
    logger.info(f"Filtri attivi: max_length={max_length}" + (", require_bw=True" if require_bw else ""))

    if max_samples:
         logger.warning("L'uso di --max_samples limita l'output ma NON garantisce la diversità per source.")
         logger.warning("Per ottenere un numero fisso di campioni DIVERSIFICATI, ometti --max_samples e usa split_dataset.py sull'output completo.")


    filtered_data = []
    processed_ids = set()
    total_processed_count = 0
    filtered_count_length = 0
    filtered_count_bw = 0
    final_accepted_count = 0

    for filename in tqdm(input_files, desc="Processing Chunks"):
        logger.debug(f"Processing file: {filename}")
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"Errore durante l'apertura o lettura del file {filename}: {e}")
            continue

        for item in data:
            total_processed_count += 1
            item_id_obj = item.get("filename", {})
            item_id = item_id_obj.get("file", f"unknown_{total_processed_count}")

            # --- USA LA CHIAVE 'xml' O 'svg'->'data' ---
            # Privilegia 'xml' se esiste, altrimenti usa 'svg'->'data'
            svg_data_string = item.get("xml")
            if svg_data_string is None:
                 svg_node = item.get("svg", {})
                 svg_data_string = svg_node.get("data")
            # -----------------------------------------

            caption_node = item.get("caption", {})
            caption = caption_node.get("short") or caption_node.get("long") or caption_node.get("blip2", "")

            source = item.get("source", "unknown")

            unique_id = f"{source}_{item_id}"

            if not item_id or not svg_data_string or not caption:
                # logger.debug(f"Skipping item {unique_id} due a dati mancanti.")
                continue

            if isinstance(unique_id, str) and unique_id.endswith('.svg'):
                 unique_id = unique_id[:-4]

            if unique_id in processed_ids:
                continue

            # --- APPLICA I NUOVI FILTRI ---
            # 1. Filtro Lunghezza
            if len(svg_data_string) > max_length:
                # logger.debug(f"Item {unique_id} scartato per lunghezza > {max_length}")
                filtered_count_length += 1
                continue

            # 2. Filtro Bianco/Nero (se richiesto)
            if require_bw:
                if not is_likely_black_and_white(svg_data_string):
                    # logger.debug(f"Item {unique_id} scartato perchè non B/N.")
                    filtered_count_bw += 1
                    continue
            # --- FINE FILTRI ---

            # Se l'item passa tutti i filtri
            width = item.get("width") # Mantiene width/height se presenti
            height = item.get("height")

            output_data = {
                "id": unique_id,
                "caption": caption,
                "xml": svg_data_string, # Salva la stringa XML originale/pulita
                "width": width,
                "height": height,
                "source": source
                # Rimosso "paths_approx"
            }
            filtered_data.append(output_data)
            processed_ids.add(unique_id)
            final_accepted_count += 1

            # Controlla max_samples alla fine del ciclo item (opzionale)
            if max_samples is not None and len(filtered_data) >= max_samples:
                logger.info(f"Raggiunto il limite massimo di {max_samples} campioni.")
                break # Esce dal ciclo interno (items)

        # Controlla max_samples alla fine del ciclo file (opzionale)
        if max_samples is not None and len(filtered_data) >= max_samples:
            break # Esce dal ciclo esterno (files)

    logger.info(f"Processati {total_processed_count} items in totale.")
    logger.info(f"Scartati per lunghezza > {max_length}: {filtered_count_length}")
    if require_bw:
        logger.info(f"Scartati perchè non B/N (euristico): {filtered_count_bw}")
    logger.info(f"Campioni accettati finali: {final_accepted_count}")

    # Salva i dati filtrati (tutti quelli che hanno passato i filtri,
    # a meno che max_samples non sia stato raggiunto)
    try:
        logger.info(f"Salvataggio di {len(filtered_data)} campioni filtrati in: {output_file}")
        os.makedirs(os.path.dirname(output_file) or '.', exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, indent=2, ensure_ascii=False)
        logger.info("Salvataggio completato.")
    except Exception as e:
        logger.error(f"Errore durante il salvataggio del file {output_file}: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Filter SVG dataset based on XML string length and optional B/W heuristic.')
    parser.add_argument('--input_pattern', type=str, required=True, help='Glob pattern for input JSON chunk files (e.g., "chunks/chunk*.json")')
    parser.add_argument('--output_file', type=str, required=True, help='Path for the output filtered JSON file.')
    parser.add_argument('--max_length', type=int, default=512, help='Maximum allowed length for the XML string (default: 512).')
    parser.add_argument('--require_bw', action='store_true', help='Apply the heuristic filter to keep only likely black/white SVGs.')
    parser.add_argument('--max_samples', type=int, default=None, help='(Optional) Maximum number of samples to save. If set, diversity is not guaranteed. Omit this to process all data and use split_dataset.py later for diverse sampling.')

    # Rimosso min/max_paths_approx
    # parser.add_argument('--min_paths_approx', type=int, default=1, help='Minimum number of approximate path segments (count of "d=").')
    # parser.add_argument('--max_paths_approx', type=int, default=15, help='Maximum number of approximate path segments (count of "d=").')

    args = parser.parse_args()

    filter_dataset(
        args.input_pattern,
        args.output_file,
        max_length=args.max_length,
        require_bw=args.require_bw,
        max_samples=args.max_samples
    )