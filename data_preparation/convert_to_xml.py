#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per convertire i dataset esistenti in formato XML completo.
Utilizza la funzione de_parser per convertire i dati SVG semplificati in XML validi.
"""

import os
import json
import argparse
import logging
from tqdm import tqdm

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def de_parser(data):
    """
    Converte i dati SVG semplificati in XML validi.
    
    Args:
        data: Stringa SVG semplificata
        
    Returns:
        Stringa XML valida
    """
    # Header
    res = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<svg viewBox=\"0 0 512 512\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\">\n"

    data = data.replace("style=", "<path style=\"")
    data = data.replace("color", "fill")
    data = data.replace("\t", "\" d=\"")
    data = data.replace("\n", "Z\" />\n")

    res += data

    # Footer
    res += "</svg>"

    return res

def convert_dataset(input_file, output_file):
    """
    Converte un dataset esistente in formato XML completo.
    
    Args:
        input_file: Percorso al file JSON di input
        output_file: Percorso al file JSON di output
    """
    logger.info(f"Caricamento dataset da: {input_file}")
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Caricati {len(data)} esempi.")
        
        # Converti i dati SVG in XML validi
        converted_data = []
        for item in tqdm(data, desc="Conversione in XML"):
            svg_data = item.get("xml", "")
            
            if not svg_data:
                logger.warning(f"Esempio {item.get('id', 'unknown')} senza dati SVG.")
                continue
            
            # Converti i dati SVG in XML validi
            xml_data = de_parser(svg_data)
            
            # Crea un nuovo item con i dati convertiti
            new_item = item.copy()
            new_item["xml"] = xml_data
            
            converted_data.append(new_item)
        
        logger.info(f"Convertiti {len(converted_data)} esempi.")
        
        # Salva i dati convertiti
        logger.info(f"Salvataggio dataset convertito in: {output_file}")
        os.makedirs(os.path.dirname(output_file) or '.', exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
        
        logger.info("Conversione completata con successo.")
    
    except Exception as e:
        logger.error(f"Errore durante la conversione: {e}")

def main():
    parser = argparse.ArgumentParser(description="Converti dataset esistenti in formato XML completo.")
    parser.add_argument("--input_file", type=str, required=True, help="Percorso al file JSON di input.")
    parser.add_argument("--output_file", type=str, required=True, help="Percorso al file JSON di output.")
    
    args = parser.parse_args()
    convert_dataset(args.input_file, args.output_file)

if __name__ == "__main__":
    main()
